{"name": "audiv-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/clerk-js": "^4.65.5", "@clerk/nextjs": "5.1.5", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.3.2", "@mui/material": "^5.15.20", "@mui/x-charts": "^7.7.1", "@nextui-org/progress": "^2.0.33", "@nextui-org/react": "^2.4.6", "@prisma/client": "^5.6.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-tooltip": "^1.1.2", "@shadcn/ui": "^0.0.4", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/supabase-js": "^2.48.1", "@tanstack/react-query": "^5.17.15", "@tanstack/react-table": "^8.20.1", "@types/md5": "^2.3.5", "@types/pdf-parse": "^1.1.4", "axios": "^1.6.7", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "embla-carousel-react": "^8.0.0-rc22", "framer-motion": "^11.3.21", "fs": "^0.0.1-security", "init": "^0.1.2", "langchain": "^0.1.4", "lucide-react": "^0.294.0", "marked": "^13.0.3", "md5": "^2.3.0", "nanoid": "^5.0.4", "next": "^14.2.4", "next-themes": "^0.2.1", "pdf-parse": "^1.1.1", "pdfjs-dist": "^4.10.38", "react": "^18", "react-audio-player": "^0.17.0", "react-color": "^2.19.3", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-hook-form": "^7.49.0", "retell-client-js-sdk": "^2.0.0", "retell-sdk": "^4.19.0", "shadcn-ui": "^0.4.1", "sharp": "^0.33.2", "sonner": "^1.4.41", "tailwind-merge": "^2.1.0", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "yarn": "^1.22.22", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-color": "^3.0.12", "@types/react-dom": "^18", "@types/regenerator-runtime": "^0.13.5", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.3", "eslint-config-prettier": "^10.0.1", "postcss": "^8", "prisma": "^5.15.0", "tailwindcss": "^3.3.0", "typescript": "^5"}, "browser": {"fs": false, "os": false, "path": false}}