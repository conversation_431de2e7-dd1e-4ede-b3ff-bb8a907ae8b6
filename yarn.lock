# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@alloc/quick-lru@^5.2.0":
  version "5.2.0"
  resolved "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz"
  integrity sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==

"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz"
  integrity sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@antfu/ni@^0.21.4":
  version "0.21.12"
  resolved "https://registry.npmjs.org/@antfu/ni/-/ni-0.21.12.tgz"
  integrity sha512-2aDL3WUv8hMJb2L3r/PIQWsTLyq7RQr3v9xD16fiz6O8ys1xEyLhhTOv8gxtZvJiTzjTF5pHoArvRdesGL1DMQ==

"@anthropic-ai/sdk@^0.9.1":
  version "0.9.1"
  resolved "https://registry.npmjs.org/@anthropic-ai/sdk/-/sdk-0.9.1.tgz"
  integrity sha512-wa1meQ2WSfoY8Uor3EdrJq0jTiZJoKoSii2ZVWRY1oN4Tlr5s59pADg9T79FTbPe1/se5c3pBeZgJL63wmuoBA==
  dependencies:
    "@types/node" "^18.11.18"
    "@types/node-fetch" "^2.6.4"
    abort-controller "^3.0.0"
    agentkeepalive "^4.2.1"
    digest-fetch "^1.3.0"
    form-data-encoder "1.7.2"
    formdata-node "^4.3.2"
    node-fetch "^2.6.7"
    web-streams-polyfill "^3.2.1"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.26.2":
  version "7.26.2"
  resolved "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.2.tgz"
  integrity sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==
  dependencies:
    "@babel/helper-validator-identifier" "^7.25.9"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/compat-data@^7.26.5":
  version "7.26.8"
  resolved "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.26.8.tgz"
  integrity sha512-oH5UPLMWR3L2wEFLnFJ1TZXqHufiTKAiLfqw5zkhS4dKXLJ10yVztfil/twG8EDTA4F/tvVNw9nOl4ZMslB8rQ==

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.22.1":
  version "7.26.9"
  resolved "https://registry.npmjs.org/@babel/core/-/core-7.26.9.tgz"
  integrity sha512-lWBYIrF7qK5+GjY5Uy+/hEgp8OJWOD/rpy74GplYRhEauvbHDeFB8t5hPOZxCZ0Oxf4Cc36tK51/l3ymJysrKw==
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.26.2"
    "@babel/generator" "^7.26.9"
    "@babel/helper-compilation-targets" "^7.26.5"
    "@babel/helper-module-transforms" "^7.26.0"
    "@babel/helpers" "^7.26.9"
    "@babel/parser" "^7.26.9"
    "@babel/template" "^7.26.9"
    "@babel/traverse" "^7.26.9"
    "@babel/types" "^7.26.9"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.26.9":
  version "7.26.9"
  resolved "https://registry.npmjs.org/@babel/generator/-/generator-7.26.9.tgz"
  integrity sha512-kEWdzjOAUMW4hAyrzJ0ZaTOu9OmpyDIQicIh0zg0EEcEkYXZb2TjtBhnHi2ViX7PKwZqF4xwqfAm299/QMP3lg==
  dependencies:
    "@babel/parser" "^7.26.9"
    "@babel/types" "^7.26.9"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/helper-annotate-as-pure@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.25.9.tgz"
  integrity sha512-gv7320KBUFJz1RnylIg5WWYPRXKZ884AGkYpgpWW02TH66Dl+HaC1t1CKd0z3R4b6hdYEcmrNZHUmfCP+1u3/g==
  dependencies:
    "@babel/types" "^7.25.9"

"@babel/helper-compilation-targets@^7.26.5":
  version "7.26.5"
  resolved "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.26.5.tgz"
  integrity sha512-IXuyn5EkouFJscIDuFF5EsiSolseme1s0CZB+QxVugqJLYmKdxI1VfIBOst0SUu4rnk2Z7kqTwmoO1lp3HIfnA==
  dependencies:
    "@babel/compat-data" "^7.26.5"
    "@babel/helper-validator-option" "^7.25.9"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.25.9":
  version "7.26.9"
  resolved "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.26.9.tgz"
  integrity sha512-ubbUqCofvxPRurw5L8WTsCLSkQiVpov4Qx0WMA+jUN+nXBK8ADPlJO1grkFw5CWKC5+sZSOfuGMdX1aI1iT9Sg==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-member-expression-to-functions" "^7.25.9"
    "@babel/helper-optimise-call-expression" "^7.25.9"
    "@babel/helper-replace-supers" "^7.26.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"
    "@babel/traverse" "^7.26.9"
    semver "^6.3.1"

"@babel/helper-member-expression-to-functions@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.25.9.tgz"
  integrity sha512-wbfdZ9w5vk0C0oyHqAJbc62+vet5prjj01jjJ8sKn3j9h3MQQlflEdXYvuqRWjHnM12coDEqiC1IRCi0U/EKwQ==
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-imports@^7.16.7", "@babel/helper-module-imports@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz"
  integrity sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-transforms@^7.26.0":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.26.0.tgz"
  integrity sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-optimise-call-expression@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.25.9.tgz"
  integrity sha512-FIpuNaz5ow8VyrYcnXQTDRGvV6tTjkNtCK/RYNDXGSLlUD6cBuQTSw43CShGxjvfBTfcUA/r6UhUCbtYqkhcuQ==
  dependencies:
    "@babel/types" "^7.25.9"

"@babel/helper-plugin-utils@^7.25.9", "@babel/helper-plugin-utils@^7.26.5":
  version "7.26.5"
  resolved "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz"
  integrity sha512-RS+jZcRdZdRFzMyr+wcsaqOmld1/EqTghfaBGQQd/WnRdzdlvSZ//kF7U8VQTxf1ynZ4cjUcYgjVGx13ewNPMg==

"@babel/helper-replace-supers@^7.26.5":
  version "7.26.5"
  resolved "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.26.5.tgz"
  integrity sha512-bJ6iIVdYX1YooY2X7w1q6VITt+LnUILtNk7zT78ykuwStx8BauCzxvFqFaHjOpW1bVnSUM1PN1f0p5P21wHxvg==
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.25.9"
    "@babel/helper-optimise-call-expression" "^7.25.9"
    "@babel/traverse" "^7.26.5"

"@babel/helper-skip-transparent-expression-wrappers@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.25.9.tgz"
  integrity sha512-K4Du3BFa3gvyhzgPcntrkDgZzQaq6uozzcpGbOO1OEJaI+EJdqWIMTLgFgQf6lrfiDFo5FU+BxKepI9RmZqahA==
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-string-parser@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz"
  integrity sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==

"@babel/helper-validator-identifier@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz"
  integrity sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==

"@babel/helper-validator-option@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.25.9.tgz"
  integrity sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==

"@babel/helpers@^7.26.9":
  version "7.26.9"
  resolved "https://registry.npmjs.org/@babel/helpers/-/helpers-7.26.9.tgz"
  integrity sha512-Mz/4+y8udxBKdmzt/UjPACs4G3j5SshJJEFFKxlCGPydG4JAHXxjWjAwjd09tf6oINvl1VfMJo+nB7H2YKQ0dA==
  dependencies:
    "@babel/template" "^7.26.9"
    "@babel/types" "^7.26.9"

"@babel/parser@^7.22.6", "@babel/parser@^7.26.9":
  version "7.26.9"
  resolved "https://registry.npmjs.org/@babel/parser/-/parser-7.26.9.tgz"
  integrity sha512-81NWa1njQblgZbQHxWHpxxCzNsa3ZwvFqpUg7P+NNUU6f3UU2jBEg4OlF/J6rl8+PQGh1q6/zWScd001YwcA5A==
  dependencies:
    "@babel/types" "^7.26.9"

"@babel/plugin-syntax-typescript@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.25.9.tgz"
  integrity sha512-hjMgRy5hb8uJJjUcdWunWVcoi9bGpJp8p5Ol1229PoN6aytsLwNMgmdftO23wnCLMfVmTwZDWMPNq/D1SY60JQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-typescript@^7.22.5":
  version "7.26.8"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.26.8.tgz"
  integrity sha512-bME5J9AC8ChwA7aEPJ6zym3w7aObZULHhbNLU0bKUhKsAkylkzUdq+0kdymh9rzi8nlNFl2bmldFBCKNJBUpuw==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.26.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"
    "@babel/plugin-syntax-typescript" "^7.25.9"

"@babel/runtime@^7.12.5", "@babel/runtime@^7.18.3", "@babel/runtime@^7.20.13", "@babel/runtime@^7.23.9", "@babel/runtime@^7.25.7", "@babel/runtime@^7.26.0", "@babel/runtime@^7.5.5", "@babel/runtime@^7.8.7":
  version "7.26.9"
  resolved "https://registry.npmjs.org/@babel/runtime/-/runtime-7.26.9.tgz"
  integrity sha512-aA63XwOkcl4xxQa3HjPMqOP6LiK0ZDv3mUPYEFXkpHbaFjtGggE1A61FjFzJnB+p7/oy2gA8E+rcBNl/zC1tMg==
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/template@^7.26.9":
  version "7.26.9"
  resolved "https://registry.npmjs.org/@babel/template/-/template-7.26.9.tgz"
  integrity sha512-qyRplbeIpNZhmzOysF/wFMuP9sctmh2cFzRAZOn1YapxBsE1i9bJIY586R/WBLfLcmcBlM8ROBiQURnnNy+zfA==
  dependencies:
    "@babel/code-frame" "^7.26.2"
    "@babel/parser" "^7.26.9"
    "@babel/types" "^7.26.9"

"@babel/traverse@^7.25.9", "@babel/traverse@^7.26.5", "@babel/traverse@^7.26.9":
  version "7.26.9"
  resolved "https://registry.npmjs.org/@babel/traverse/-/traverse-7.26.9.tgz"
  integrity sha512-ZYW7L+pL8ahU5fXmNbPF+iZFHCv5scFak7MZ9bwaRPLUhHh7QQEMjZUg0HevihoqCM5iSYHN61EyCoZvqC+bxg==
  dependencies:
    "@babel/code-frame" "^7.26.2"
    "@babel/generator" "^7.26.9"
    "@babel/parser" "^7.26.9"
    "@babel/template" "^7.26.9"
    "@babel/types" "^7.26.9"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.25.9", "@babel/types@^7.26.9":
  version "7.26.9"
  resolved "https://registry.npmjs.org/@babel/types/-/types-7.26.9.tgz"
  integrity sha512-Y3IR1cRnOxOCDvMmNiym7XpXQ93iGDDPHx+Zj+NM+rg0fBaShfQLkg+hKPaZCEvg5N/LeCo4+Rj/i3FuJsIQaw==
  dependencies:
    "@babel/helper-string-parser" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"

"@bufbuild/protobuf@^1.10.0":
  version "1.10.0"
  resolved "https://registry.npmjs.org/@bufbuild/protobuf/-/protobuf-1.10.0.tgz"
  integrity sha512-QDdVFLoN93Zjg36NoQPZfsVH9tZew7wKDKyV5qRdj8ntT4wQCOradQjRaTdwMhWUYsgKsvCINKKm87FdEk96Ag==

"@clerk/backend@1.2.3":
  version "1.2.3"
  resolved "https://registry.npmjs.org/@clerk/backend/-/backend-1.2.3.tgz"
  integrity sha512-tj812eTTn2ewXMgr4jwFjpqoXZRF2LMw9UBT+Nat0lmXw55sDA5ou2McLZ67e62WNZwbrCUa51MGKSBhrWnZcA==
  dependencies:
    "@clerk/shared" "2.3.0"
    "@clerk/types" "4.6.0"
    cookie "0.5.0"
    snakecase-keys "5.4.4"
    tslib "2.4.1"

"@clerk/clerk-js@^4.65.5":
  version "4.73.13"
  resolved "https://registry.npmjs.org/@clerk/clerk-js/-/clerk-js-4.73.13.tgz"
  integrity sha512-hCu4JgZeQM1pjZIF+RJUqZ0ja02FI8f0o9KdQfVnrx/D97sSkx9lkujdzGAmUGhoeJozGYiW5Hq5x3YoATVumg==
  dependencies:
    "@clerk/localizations" "1.28.9"
    "@clerk/shared" "1.4.2"
    "@clerk/types" "3.65.4"
    "@emotion/cache" "11.11.0"
    "@emotion/react" "11.11.1"
    "@floating-ui/react" "0.25.4"
    "@zxcvbn-ts/core" "3.0.4"
    "@zxcvbn-ts/language-common" "3.0.4"
    browser-tabs-lock "1.2.15"
    copy-to-clipboard "3.3.3"
    core-js "3.26.1"
    dequal "2.0.3"
    qrcode.react "3.1.0"
    qs "6.11.0"
    regenerator-runtime "0.13.11"

"@clerk/clerk-react@5.2.4":
  version "5.2.4"
  resolved "https://registry.npmjs.org/@clerk/clerk-react/-/clerk-react-5.2.4.tgz"
  integrity sha512-TaSjf3pdxUKQIDmwi6JkJDVGwHbs7pTeiwEr2/JksMrQnW6zMIutsEhJfW10dY1hOwJeDoSxGCkHw+7Br2rktw==
  dependencies:
    "@clerk/shared" "2.3.0"
    "@clerk/types" "4.6.0"
    tslib "2.4.1"

"@clerk/localizations@1.28.9":
  version "1.28.9"
  resolved "https://registry.npmjs.org/@clerk/localizations/-/localizations-1.28.9.tgz"
  integrity sha512-yEcO5eQy9+P9RxH1NIA5VQMRiCyUcQT/25YlwR7UL7lGwQ/v58Rue47HawpQ1PqA4RdklDqOePU4eVnt4Y2HGA==
  dependencies:
    "@clerk/types" "3.65.4"

"@clerk/nextjs@5.1.5":
  version "5.1.5"
  resolved "https://registry.npmjs.org/@clerk/nextjs/-/nextjs-5.1.5.tgz"
  integrity sha512-q/4PvWrIt4cO9dwgUyJ/gN/fWbS2GnfKK7j32cn6LBObVqUIiQ+J5Q+lp75q+tzIHyxFJx+MNNTnFif2OrvV6A==
  dependencies:
    "@clerk/backend" "1.2.3"
    "@clerk/clerk-react" "5.2.4"
    "@clerk/shared" "2.3.0"
    "@clerk/types" "4.6.0"
    crypto-js "4.2.0"
    path-to-regexp "6.2.2"
    tslib "2.4.1"

"@clerk/shared@1.4.2":
  version "1.4.2"
  resolved "https://registry.npmjs.org/@clerk/shared/-/shared-1.4.2.tgz"
  integrity sha512-R+OkzCtnNU7sn/F6dBfdY5lKs84TN785VZdBBefmyr7zsXcFEqbCcfQzyvgtIS28Ln5SifFEBoAyYR334IXO8w==
  dependencies:
    glob-to-regexp "0.4.1"
    js-cookie "3.0.1"
    swr "2.2.0"

"@clerk/shared@2.3.0":
  version "2.3.0"
  resolved "https://registry.npmjs.org/@clerk/shared/-/shared-2.3.0.tgz"
  integrity sha512-V/49MoOrALzpu0BbhYDCcKQYIjrHnhRa7QFho9+4wm94oCJgc9j3N5wxndJwj3Ur/fmIyBnjwMzDAT2nZZj47g==
  dependencies:
    "@clerk/types" "4.6.0"
    glob-to-regexp "0.4.1"
    js-cookie "3.0.5"
    std-env "^3.7.0"
    swr "^2.2.0"

"@clerk/types@3.65.4":
  version "3.65.4"
  resolved "https://registry.npmjs.org/@clerk/types/-/types-3.65.4.tgz"
  integrity sha512-sDNt7kp/rYVUF+wJvLPlH+YMm/iKgtARVmTZBU0rgXKPrEKJA1vdmT2UCe4geO1lgqHCi1lXTzm2O90NCWC9yA==
  dependencies:
    csstype "3.1.1"

"@clerk/types@4.6.0":
  version "4.6.0"
  resolved "https://registry.npmjs.org/@clerk/types/-/types-4.6.0.tgz"
  integrity sha512-kowqVGqLfu0Zl2Pteum70MfkGHqBUoHHeR+u2+yWVl1lKHLCiyY1u8ntYBEIolAylBaQNDuRzxyMIDPSxjPE8g==
  dependencies:
    csstype "3.1.1"

"@emotion/babel-plugin@^11.11.0", "@emotion/babel-plugin@^11.13.5":
  version "11.13.5"
  resolved "https://registry.npmjs.org/@emotion/babel-plugin/-/babel-plugin-11.13.5.tgz"
  integrity sha512-pxHCpT2ex+0q+HH91/zsdHkw/lXd468DIN2zvfvLtPKLLMo6gQj7oLObq8PhkrxOZb/gGCq03S3Z7PDhS8pduQ==
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/runtime" "^7.18.3"
    "@emotion/hash" "^0.9.2"
    "@emotion/memoize" "^0.9.0"
    "@emotion/serialize" "^1.3.3"
    babel-plugin-macros "^3.1.0"
    convert-source-map "^1.5.0"
    escape-string-regexp "^4.0.0"
    find-root "^1.1.0"
    source-map "^0.5.7"
    stylis "4.2.0"

"@emotion/cache@^11.11.0", "@emotion/cache@11.11.0":
  version "11.11.0"
  resolved "https://registry.npmjs.org/@emotion/cache/-/cache-11.11.0.tgz"
  integrity sha512-P34z9ssTCBi3e9EI1ZsWpNHcfY1r09ZO0rZbRO2ob3ZQMnFI35jB536qoXbkdesr5EUhYi22anuEJuyxifaqAQ==
  dependencies:
    "@emotion/memoize" "^0.8.1"
    "@emotion/sheet" "^1.2.2"
    "@emotion/utils" "^1.2.1"
    "@emotion/weak-memoize" "^0.3.1"
    stylis "4.2.0"

"@emotion/cache@^11.13.5":
  version "11.14.0"
  resolved "https://registry.npmjs.org/@emotion/cache/-/cache-11.14.0.tgz"
  integrity sha512-L/B1lc/TViYk4DcpGxtAVbx0ZyiKM5ktoIyafGkH6zg/tj+mA+NE//aPYKG0k8kCHSHVJrpLpcAlOBEXQ3SavA==
  dependencies:
    "@emotion/memoize" "^0.9.0"
    "@emotion/sheet" "^1.4.0"
    "@emotion/utils" "^1.4.2"
    "@emotion/weak-memoize" "^0.4.0"
    stylis "4.2.0"

"@emotion/cache@^11.14.0":
  version "11.14.0"
  resolved "https://registry.npmjs.org/@emotion/cache/-/cache-11.14.0.tgz"
  integrity sha512-L/B1lc/TViYk4DcpGxtAVbx0ZyiKM5ktoIyafGkH6zg/tj+mA+NE//aPYKG0k8kCHSHVJrpLpcAlOBEXQ3SavA==
  dependencies:
    "@emotion/memoize" "^0.9.0"
    "@emotion/sheet" "^1.4.0"
    "@emotion/utils" "^1.4.2"
    "@emotion/weak-memoize" "^0.4.0"
    stylis "4.2.0"

"@emotion/hash@^0.9.2":
  version "0.9.2"
  resolved "https://registry.npmjs.org/@emotion/hash/-/hash-0.9.2.tgz"
  integrity sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==

"@emotion/is-prop-valid@*", "@emotion/is-prop-valid@^1.3.0":
  version "1.3.1"
  resolved "https://registry.npmjs.org/@emotion/is-prop-valid/-/is-prop-valid-1.3.1.tgz"
  integrity sha512-/ACwoqx7XQi9knQs/G0qKvv5teDMhD7bXYns9N/wM8ah8iNb8jZ2uNO0YOgiq2o2poIvVtJS2YALasQuMSQ7Kw==
  dependencies:
    "@emotion/memoize" "^0.9.0"

"@emotion/memoize@^0.8.1":
  version "0.8.1"
  resolved "https://registry.npmjs.org/@emotion/memoize/-/memoize-0.8.1.tgz"
  integrity sha512-W2P2c/VRW1/1tLox0mVUalvnWXxavmv/Oum2aPsRcoDJuob75FC3Y8FbpfLwUegRcxINtGUMPq0tFCvYNTBXNA==

"@emotion/memoize@^0.9.0":
  version "0.9.0"
  resolved "https://registry.npmjs.org/@emotion/memoize/-/memoize-0.9.0.tgz"
  integrity sha512-30FAj7/EoJ5mwVPOWhAyCX+FPfMDrVecJAM+Iw9NRoSl4BBAQeqj4cApHHUXOVvIPgLVDsCFoz/hGD+5QQD1GQ==

"@emotion/react@^11.0.0-rc.0", "@emotion/react@^11.11.4", "@emotion/react@^11.4.1", "@emotion/react@^11.5.0", "@emotion/react@^11.9.0":
  version "11.14.0"
  resolved "https://registry.npmjs.org/@emotion/react/-/react-11.14.0.tgz"
  integrity sha512-O000MLDBDdk/EohJPFUqvnp4qnHeYkVP5B0xEG0D/L7cOKP9kefu2DXn8dj74cQfsEzUqh+sr1RzFqiL1o+PpA==
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@emotion/babel-plugin" "^11.13.5"
    "@emotion/cache" "^11.14.0"
    "@emotion/serialize" "^1.3.3"
    "@emotion/use-insertion-effect-with-fallbacks" "^1.2.0"
    "@emotion/utils" "^1.4.2"
    "@emotion/weak-memoize" "^0.4.0"
    hoist-non-react-statics "^3.3.1"

"@emotion/react@11.11.1":
  version "11.11.1"
  resolved "https://registry.npmjs.org/@emotion/react/-/react-11.11.1.tgz"
  integrity sha512-5mlW1DquU5HaxjLkfkGN1GA/fvVGdyHURRiX/0FHl2cfIfRxSOfmxEH5YS43edp0OldZrZ+dkBKbngxcNCdZvA==
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@emotion/babel-plugin" "^11.11.0"
    "@emotion/cache" "^11.11.0"
    "@emotion/serialize" "^1.1.2"
    "@emotion/use-insertion-effect-with-fallbacks" "^1.0.1"
    "@emotion/utils" "^1.2.1"
    "@emotion/weak-memoize" "^0.3.1"
    hoist-non-react-statics "^3.3.1"

"@emotion/serialize@^1.1.2", "@emotion/serialize@^1.3.3":
  version "1.3.3"
  resolved "https://registry.npmjs.org/@emotion/serialize/-/serialize-1.3.3.tgz"
  integrity sha512-EISGqt7sSNWHGI76hC7x1CksiXPahbxEOrC5RjmFRJTqLyEK9/9hZvBbiYn70dw4wuwMKiEMCUlR6ZXTSWQqxA==
  dependencies:
    "@emotion/hash" "^0.9.2"
    "@emotion/memoize" "^0.9.0"
    "@emotion/unitless" "^0.10.0"
    "@emotion/utils" "^1.4.2"
    csstype "^3.0.2"

"@emotion/sheet@^1.2.2", "@emotion/sheet@^1.4.0":
  version "1.4.0"
  resolved "https://registry.npmjs.org/@emotion/sheet/-/sheet-1.4.0.tgz"
  integrity sha512-fTBW9/8r2w3dXWYM4HCB1Rdp8NLibOw2+XELH5m5+AkWiL/KqYX6dc0kKYlaYyKjrQ6ds33MCdMPEwgs2z1rqg==

"@emotion/styled@^11.11.5", "@emotion/styled@^11.3.0", "@emotion/styled@^11.8.1":
  version "11.14.0"
  resolved "https://registry.npmjs.org/@emotion/styled/-/styled-11.14.0.tgz"
  integrity sha512-XxfOnXFffatap2IyCeJyNov3kiDQWoR08gPUQxvbL7fxKryGBKUZUkG6Hz48DZwVrJSVh9sJboyV1Ds4OW6SgA==
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@emotion/babel-plugin" "^11.13.5"
    "@emotion/is-prop-valid" "^1.3.0"
    "@emotion/serialize" "^1.3.3"
    "@emotion/use-insertion-effect-with-fallbacks" "^1.2.0"
    "@emotion/utils" "^1.4.2"

"@emotion/unitless@^0.10.0":
  version "0.10.0"
  resolved "https://registry.npmjs.org/@emotion/unitless/-/unitless-0.10.0.tgz"
  integrity sha512-dFoMUuQA20zvtVTuxZww6OHoJYgrzfKM1t52mVySDJnMSEa08ruEvdYQbhvyu6soU+NeLVd3yKfTfT0NeV6qGg==

"@emotion/use-insertion-effect-with-fallbacks@^1.0.1", "@emotion/use-insertion-effect-with-fallbacks@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@emotion/use-insertion-effect-with-fallbacks/-/use-insertion-effect-with-fallbacks-1.2.0.tgz"
  integrity sha512-yJMtVdH59sxi/aVJBpk9FQq+OR8ll5GT8oWd57UpeaKEVGab41JWaCFA7FRLoMLloOZF/c/wsPoe+bfGmRKgDg==

"@emotion/utils@^1.2.1", "@emotion/utils@^1.4.2":
  version "1.4.2"
  resolved "https://registry.npmjs.org/@emotion/utils/-/utils-1.4.2.tgz"
  integrity sha512-3vLclRofFziIa3J2wDh9jjbkUz9qk5Vi3IZ/FSTKViB0k+ef0fPV7dYrUIugbgupYDx7v9ud/SjrtEP8Y4xLoA==

"@emotion/weak-memoize@^0.3.1":
  version "0.3.1"
  resolved "https://registry.npmjs.org/@emotion/weak-memoize/-/weak-memoize-0.3.1.tgz"
  integrity sha512-EsBwpc7hBUJWAsNPBmJy4hxWx12v6bshQsldrVmjxJoc3isbxhOrF2IcCpaXxfvq03NwkI7sbsOLXbYuqF/8Ww==

"@emotion/weak-memoize@^0.4.0":
  version "0.4.0"
  resolved "https://registry.npmjs.org/@emotion/weak-memoize/-/weak-memoize-0.4.0.tgz"
  integrity sha512-snKqtPW01tN0ui7yu9rGv69aJXr/a/Ywvl11sUjNtEcRc+ng/mQriFL0wLXMef74iHa/EkftbDzU9F8iFbH+zg==

"@eslint-community/eslint-utils@^4.2.0":
  version "4.4.1"
  resolved "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.4.1.tgz"
  integrity sha512-s3O3waFUrMV8P/XaF/+ZTp1X9XBZW1a4B97ZnjQF2KYWaFD2A8KyFBsrsfSjEmjn3RGWAIuvlneuZm3CUK3jbA==
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.6.1":
  version "4.12.1"
  resolved "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz"
  integrity sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==

"@eslint/eslintrc@^2.1.4":
  version "2.1.4"
  resolved "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.4.tgz"
  integrity sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.6.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.57.1":
  version "8.57.1"
  resolved "https://registry.npmjs.org/@eslint/js/-/js-8.57.1.tgz"
  integrity sha512-d9zaMRSTIKDLhctzH12MtXvJKSSUhaHcjV+2Z+GK+EEY7XKpP5yR4x+N3TAcHTcu963nIr+TMcCb4DBCYX1z6Q==

"@floating-ui/core@^1.6.0":
  version "1.6.9"
  resolved "https://registry.npmjs.org/@floating-ui/core/-/core-1.6.9.tgz"
  integrity sha512-uMXCuQ3BItDUbAMhIXw7UPXRfAlOAvZzdK9BWpE60MCn+Svt3aLn9jsPTi/WNGlRUu2uI0v5S7JiIUsbsvh3fw==
  dependencies:
    "@floating-ui/utils" "^0.2.9"

"@floating-ui/dom@^1.0.0":
  version "1.6.13"
  resolved "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.6.13.tgz"
  integrity sha512-umqzocjDgNRGTuO7Q8CU32dkHkECqI8ZdMZ5Swb6QAM0t5rnlrN3lGo1hdpscRd3WS8T6DKYK4ephgIH9iRh3w==
  dependencies:
    "@floating-ui/core" "^1.6.0"
    "@floating-ui/utils" "^0.2.9"

"@floating-ui/react-dom@^2.0.0", "@floating-ui/react-dom@^2.0.2":
  version "2.1.2"
  resolved "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.1.2.tgz"
  integrity sha512-06okr5cgPzMNBy+Ycse2A6udMi4bqwW/zgBF/rwjcNqWkyr82Mcg8b0vjX8OJpZFy/FKjJmw6wV7t44kK6kW7A==
  dependencies:
    "@floating-ui/dom" "^1.0.0"

"@floating-ui/react@0.25.4":
  version "0.25.4"
  resolved "https://registry.npmjs.org/@floating-ui/react/-/react-0.25.4.tgz"
  integrity sha512-lWRQ/UiTvSIBxohn0/2HFHEmnmOVRjl7j6XcRJuLH0ls6f/9AyHMWVzkAJFuwx0n9gaEeCmg9VccCSCJzbEJig==
  dependencies:
    "@floating-ui/react-dom" "^2.0.2"
    "@floating-ui/utils" "^0.1.1"
    tabbable "^6.0.1"

"@floating-ui/utils@^0.1.1":
  version "0.1.6"
  resolved "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.1.6.tgz"
  integrity sha512-OfX7E2oUDYxtBvsuS4e/jSn4Q9Qb6DzgeYtsAdkPZ47znpoNsMgZw0+tVijiv3uGNR6dgNlty6r9rzIzHjtd/A==

"@floating-ui/utils@^0.2.9":
  version "0.2.9"
  resolved "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.9.tgz"
  integrity sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==

"@formatjs/ecma402-abstract@2.3.3":
  version "2.3.3"
  resolved "https://registry.npmjs.org/@formatjs/ecma402-abstract/-/ecma402-abstract-2.3.3.tgz"
  integrity sha512-pJT1OkhplSmvvr6i3CWTPvC/FGC06MbN5TNBfRO6Ox62AEz90eMq+dVvtX9Bl3jxCEkS0tATzDarRZuOLw7oFg==
  dependencies:
    "@formatjs/fast-memoize" "2.2.6"
    "@formatjs/intl-localematcher" "0.6.0"
    decimal.js "10"
    tslib "2"

"@formatjs/fast-memoize@2.2.6":
  version "2.2.6"
  resolved "https://registry.npmjs.org/@formatjs/fast-memoize/-/fast-memoize-2.2.6.tgz"
  integrity sha512-luIXeE2LJbQnnzotY1f2U2m7xuQNj2DA8Vq4ce1BY9ebRZaoPB1+8eZ6nXpLzsxuW5spQxr7LdCg+CApZwkqkw==
  dependencies:
    tslib "2"

"@formatjs/icu-messageformat-parser@2.11.1":
  version "2.11.1"
  resolved "https://registry.npmjs.org/@formatjs/icu-messageformat-parser/-/icu-messageformat-parser-2.11.1.tgz"
  integrity sha512-o0AhSNaOfKoic0Sn1GkFCK4MxdRsw7mPJ5/rBpIqdvcC7MIuyUSW8WChUEvrK78HhNpYOgqCQbINxCTumJLzZA==
  dependencies:
    "@formatjs/ecma402-abstract" "2.3.3"
    "@formatjs/icu-skeleton-parser" "1.8.13"
    tslib "2"

"@formatjs/icu-skeleton-parser@1.8.13":
  version "1.8.13"
  resolved "https://registry.npmjs.org/@formatjs/icu-skeleton-parser/-/icu-skeleton-parser-1.8.13.tgz"
  integrity sha512-N/LIdTvVc1TpJmMt2jVg0Fr1F7Q1qJPdZSCs19unMskCmVQ/sa0H9L8PWt13vq+gLdLg1+pPsvBLydL1Apahjg==
  dependencies:
    "@formatjs/ecma402-abstract" "2.3.3"
    tslib "2"

"@formatjs/intl-localematcher@0.6.0":
  version "0.6.0"
  resolved "https://registry.npmjs.org/@formatjs/intl-localematcher/-/intl-localematcher-0.6.0.tgz"
  integrity sha512-4rB4g+3hESy1bHSBG3tDFaMY2CH67iT7yne1e+0CLTsGLDcmoEWWpJjjpWVaYgYfYuohIRuo0E+N536gd2ZHZA==
  dependencies:
    tslib "2"

"@hookform/resolvers@^3.3.2":
  version "3.10.0"
  resolved "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.10.0.tgz"
  integrity sha512-79Dv+3mDF7i+2ajj7SkypSKHhl1cbln1OGavqrsF7p6mbUv11xpqpacPsGDCTRvCSjEEIez2ef1NveSVL3b0Ag==

"@humanwhocodes/config-array@^0.13.0":
  version "0.13.0"
  resolved "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.13.0.tgz"
  integrity sha512-DZLEEqFWQFiyK6h5YIeynKx7JlvCYWL0cImfSRXZ9l4Sg2efkFGTuFf6vzXjK1cq6IYkU+Eg/JizXw+TD2vRNw==
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.3"
    debug "^4.3.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
  integrity sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==

"@humanwhocodes/object-schema@^2.0.3":
  version "2.0.3"
  resolved "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz"
  integrity sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==

"@icons/material@^0.2.4":
  version "0.2.4"
  resolved "https://registry.npmjs.org/@icons/material/-/material-0.2.4.tgz"
  integrity sha512-QPcGmICAPbGLGb6F/yNf/KzKqvFx8z5qx3D1yFqVAjoFmXK35EgyW+cJ57Te3CNsmzblwtzakLGFqHPqrfb4Tw==

"@img/sharp-win32-x64@0.33.5":
  version "0.33.5"
  resolved "https://registry.npmjs.org/@img/sharp-win32-x64/-/sharp-win32-x64-0.33.5.tgz"
  integrity sha512-MpY/o8/8kj+EcnxwvrP4aTJSWw/aZ7JIGR4aBeZkZw5B7/Jn+tY9/VNwtcoGmdT7GfggGIU4kygOMSbYnOrAbg==

"@internationalized/date@^3.6.0", "@internationalized/date@3.6.0":
  version "3.6.0"
  resolved "https://registry.npmjs.org/@internationalized/date/-/date-3.6.0.tgz"
  integrity sha512-+z6ti+CcJnRlLHok/emGEsWQhe7kfSmEW+/6qCzvKY67YPh7YOBfvc7+/+NXq+zJlbArg30tYpqLjNgcAYv2YQ==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@internationalized/date@^3.7.0":
  version "3.7.0"
  resolved "https://registry.npmjs.org/@internationalized/date/-/date-3.7.0.tgz"
  integrity sha512-VJ5WS3fcVx0bejE/YHfbDKR/yawZgKqn/if+oEeLqNwBtPzVB06olkfcnojTmEMX+gTpH+FlQ69SHNitJ8/erQ==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@internationalized/message@^3.1.6":
  version "3.1.6"
  resolved "https://registry.npmjs.org/@internationalized/message/-/message-3.1.6.tgz"
  integrity sha512-JxbK3iAcTIeNr1p0WIFg/wQJjIzJt9l/2KNY/48vXV7GRGZSv3zMxJsce008fZclk2cDC8y0Ig3odceHO7EfNQ==
  dependencies:
    "@swc/helpers" "^0.5.0"
    intl-messageformat "^10.1.0"

"@internationalized/number@^3.6.0":
  version "3.6.0"
  resolved "https://registry.npmjs.org/@internationalized/number/-/number-3.6.0.tgz"
  integrity sha512-PtrRcJVy7nw++wn4W2OuePQQfTqDzfusSuY1QTtui4wa7r+rGVtR75pO8CyKvHvzyQYi3Q1uO5sY0AsB4e65Bw==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@internationalized/string@^3.2.5":
  version "3.2.5"
  resolved "https://registry.npmjs.org/@internationalized/string/-/string-3.2.5.tgz"
  integrity sha512-rKs71Zvl2OKOHM+mzAFMIyqR5hI1d1O6BBkMK2/lkfg3fkmVh9Eeg0awcA8W2WqYqDOv6a86DIOlFpggwLtbuw==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz"
  integrity sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@jridgewell/gen-mapping@^0.3.2", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.8"
  resolved "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz"
  integrity sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz"
  integrity sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14":
  version "1.5.0"
  resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
  integrity sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
  integrity sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@langchain/community@~0.0.47":
  version "0.0.57"
  resolved "https://registry.npmjs.org/@langchain/community/-/community-0.0.57.tgz"
  integrity sha512-tib4UJNkyA4TPNsTNChiBtZmThVJBr7X/iooSmKeCr+yUEha2Yxly3A4OAO95Vlpj4Q+od8HAfCbZih/1XqAMw==
  dependencies:
    "@langchain/core" "~0.1.60"
    "@langchain/openai" "~0.0.28"
    expr-eval "^2.0.2"
    flat "^5.0.2"
    langsmith "~0.1.1"
    uuid "^9.0.0"
    zod "^3.22.3"
    zod-to-json-schema "^3.22.5"

"@langchain/core@>0.1.56 <0.3.0", "@langchain/core@~0.1.60":
  version "0.1.63"
  resolved "https://registry.npmjs.org/@langchain/core/-/core-0.1.63.tgz"
  integrity sha512-+fjyYi8wy6x1P+Ee1RWfIIEyxd9Ee9jksEwvrggPwwI/p45kIDTdYTblXsM13y4mNWTiACyLSdbwnPaxxdoz+w==
  dependencies:
    ansi-styles "^5.0.0"
    camelcase "6"
    decamelize "1.2.0"
    js-tiktoken "^1.0.12"
    langsmith "~0.1.7"
    ml-distance "^4.0.0"
    mustache "^4.2.0"
    p-queue "^6.6.2"
    p-retry "4"
    uuid "^9.0.0"
    zod "^3.22.4"
    zod-to-json-schema "^3.22.3"

"@langchain/core@>0.2.0 <0.3.0":
  version "0.2.36"
  resolved "https://registry.npmjs.org/@langchain/core/-/core-0.2.36.tgz"
  integrity sha512-qHLvScqERDeH7y2cLuJaSAlMwg3f/3Oc9nayRSXRU2UuaK/SOhI42cxiPLj1FnuHJSmN0rBQFkrLx02gI4mcVg==
  dependencies:
    ansi-styles "^5.0.0"
    camelcase "6"
    decamelize "1.2.0"
    js-tiktoken "^1.0.12"
    langsmith "^0.1.56-rc.1"
    mustache "^4.2.0"
    p-queue "^6.6.2"
    p-retry "4"
    uuid "^10.0.0"
    zod "^3.22.4"
    zod-to-json-schema "^3.22.3"

"@langchain/openai@~0.0.28":
  version "0.0.34"
  resolved "https://registry.npmjs.org/@langchain/openai/-/openai-0.0.34.tgz"
  integrity sha512-M+CW4oXle5fdoz2T2SwdOef8pl3/1XmUx1vjn2mXUVM/128aO0l23FMF0SNBsAbRV6P+p/TuzjodchJbi0Ht/A==
  dependencies:
    "@langchain/core" ">0.1.56 <0.3.0"
    js-tiktoken "^1.0.12"
    openai "^4.41.1"
    zod "^3.22.4"
    zod-to-json-schema "^3.22.3"

"@langchain/textsplitters@~0.0.0":
  version "0.0.3"
  resolved "https://registry.npmjs.org/@langchain/textsplitters/-/textsplitters-0.0.3.tgz"
  integrity sha512-cXWgKE3sdWLSqAa8ykbCcUsUF1Kyr5J3HOWYGuobhPEycXW4WI++d5DhzdpL238mzoEXTi90VqfSCra37l5YqA==
  dependencies:
    "@langchain/core" ">0.2.0 <0.3.0"
    js-tiktoken "^1.0.12"

"@livekit/mutex@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@livekit/mutex/-/mutex-1.1.1.tgz"
  integrity sha512-EsshAucklmpuUAfkABPxJNhzj9v2sG7JuzFDL4ML1oJQSV14sqrpTYnsaOudMAw9yOaW53NU3QQTlUQoRs4czw==

"@livekit/protocol@1.33.0":
  version "1.33.0"
  resolved "https://registry.npmjs.org/@livekit/protocol/-/protocol-1.33.0.tgz"
  integrity sha512-361mBlFgI3nvn8oSQIL38gDUBGbOSwsEOqPgX0c1Jwz75/sD/TTvPeAM4zAz6OrV5Q4vI4Ruswecnyv5SG4oig==
  dependencies:
    "@bufbuild/protobuf" "^1.10.0"

"@mui/core-downloads-tracker@^5.16.14":
  version "5.16.14"
  resolved "https://registry.npmjs.org/@mui/core-downloads-tracker/-/core-downloads-tracker-5.16.14.tgz"
  integrity sha512-sbjXW+BBSvmzn61XyTMun899E7nGPTXwqD9drm1jBUAvWEhJpPFIRxwQQiATWZnd9rvdxtnhhdsDxEGWI0jxqA==

"@mui/material@^5.15.14 || ^6.0.0", "@mui/material@^5.15.20":
  version "5.16.14"
  resolved "https://registry.npmjs.org/@mui/material/-/material-5.16.14.tgz"
  integrity sha512-eSXQVCMKU2xc7EcTxe/X/rC9QsV2jUe8eLM3MUCPYbo6V52eCE436akRIvELq/AqZpxx2bwkq7HC0cRhLB+yaw==
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@mui/core-downloads-tracker" "^5.16.14"
    "@mui/system" "^5.16.14"
    "@mui/types" "^7.2.15"
    "@mui/utils" "^5.16.14"
    "@popperjs/core" "^2.11.8"
    "@types/react-transition-group" "^4.4.10"
    clsx "^2.1.0"
    csstype "^3.1.3"
    prop-types "^15.8.1"
    react-is "^19.0.0"
    react-transition-group "^4.4.5"

"@mui/private-theming@^5.16.14":
  version "5.16.14"
  resolved "https://registry.npmjs.org/@mui/private-theming/-/private-theming-5.16.14.tgz"
  integrity sha512-12t7NKzvYi819IO5IapW2BcR33wP/KAVrU8d7gLhGHoAmhDxyXlRoKiRij3TOD8+uzk0B6R9wHUNKi4baJcRNg==
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@mui/utils" "^5.16.14"
    prop-types "^15.8.1"

"@mui/private-theming@^6.4.9":
  version "6.4.9"
  resolved "https://registry.npmjs.org/@mui/private-theming/-/private-theming-6.4.9.tgz"
  integrity sha512-LktcVmI5X17/Q5SkwjCcdOLBzt1hXuc14jYa7NPShog0GBDCDvKtcnP0V7a2s6EiVRlv7BzbWEJzH6+l/zaCxw==
  dependencies:
    "@babel/runtime" "^7.26.0"
    "@mui/utils" "^6.4.9"
    prop-types "^15.8.1"

"@mui/styled-engine@^5.16.14":
  version "5.16.14"
  resolved "https://registry.npmjs.org/@mui/styled-engine/-/styled-engine-5.16.14.tgz"
  integrity sha512-UAiMPZABZ7p8mUW4akDV6O7N3+4DatStpXMZwPlt+H/dA0lt67qawN021MNND+4QTpjaiMYxbhKZeQcyWCbuKw==
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@emotion/cache" "^11.13.5"
    csstype "^3.1.3"
    prop-types "^15.8.1"

"@mui/styled-engine@^6.5.0":
  version "6.5.0"
  resolved "https://registry.npmjs.org/@mui/styled-engine/-/styled-engine-6.5.0.tgz"
  integrity sha512-8woC2zAqF4qUDSPIBZ8v3sakj+WgweolpyM/FXf8jAx6FMls+IE4Y8VDZc+zS805J7PRz31vz73n2SovKGaYgw==
  dependencies:
    "@babel/runtime" "^7.26.0"
    "@emotion/cache" "^11.13.5"
    "@emotion/serialize" "^1.3.3"
    "@emotion/sheet" "^1.4.0"
    csstype "^3.1.3"
    prop-types "^15.8.1"

"@mui/system@^5.15.14 || ^6.0.0":
  version "6.5.0"
  resolved "https://registry.npmjs.org/@mui/system/-/system-6.5.0.tgz"
  integrity sha512-XcbBYxDS+h/lgsoGe78ExXFZXtuIlSBpn/KsZq8PtZcIkUNJInkuDqcLd2rVBQrDC1u+rvVovdaWPf2FHKJf3w==
  dependencies:
    "@babel/runtime" "^7.26.0"
    "@mui/private-theming" "^6.4.9"
    "@mui/styled-engine" "^6.5.0"
    "@mui/types" "~7.2.24"
    "@mui/utils" "^6.4.9"
    clsx "^2.1.1"
    csstype "^3.1.3"
    prop-types "^15.8.1"

"@mui/system@^5.16.14":
  version "5.16.14"
  resolved "https://registry.npmjs.org/@mui/system/-/system-5.16.14.tgz"
  integrity sha512-KBxMwCb8mSIABnKvoGbvM33XHyT+sN0BzEBG+rsSc0lLQGzs7127KWkCA6/H8h6LZ00XpBEME5MAj8mZLiQ1tw==
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@mui/private-theming" "^5.16.14"
    "@mui/styled-engine" "^5.16.14"
    "@mui/types" "^7.2.15"
    "@mui/utils" "^5.16.14"
    clsx "^2.1.0"
    csstype "^3.1.3"
    prop-types "^15.8.1"

"@mui/types@^7.2.15", "@mui/types@~7.2.24":
  version "7.2.24"
  resolved "https://registry.npmjs.org/@mui/types/-/types-7.2.24.tgz"
  integrity sha512-3c8tRt/CbWZ+pEg7QpSwbdxOk36EfmhbKf6AGZsD1EcLDLTSZoxxJ86FVtcjxvjuhdyBiWKSTGZFaXCnidO2kw==

"@mui/utils@^5.16.14", "@mui/utils@^5.16.6 || ^6.0.0":
  version "5.16.14"
  resolved "https://registry.npmjs.org/@mui/utils/-/utils-5.16.14.tgz"
  integrity sha512-wn1QZkRzSmeXD1IguBVvJJHV3s6rxJrfb6YuC9Kk6Noh9f8Fb54nUs5JRkKm+BOerRhj5fLg05Dhx/H3Ofb8Mg==
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@mui/types" "^7.2.15"
    "@types/prop-types" "^15.7.12"
    clsx "^2.1.1"
    prop-types "^15.8.1"
    react-is "^19.0.0"

"@mui/utils@^6.4.9":
  version "6.4.9"
  resolved "https://registry.npmjs.org/@mui/utils/-/utils-6.4.9.tgz"
  integrity sha512-Y12Q9hbK9g+ZY0T3Rxrx9m2m10gaphDuUMgWxyV5kNJevVxXYCLclYUCC9vXaIk1/NdNDTcW2Yfr2OGvNFNmHg==
  dependencies:
    "@babel/runtime" "^7.26.0"
    "@mui/types" "~7.2.24"
    "@types/prop-types" "^15.7.14"
    clsx "^2.1.1"
    prop-types "^15.8.1"
    react-is "^19.0.0"

"@mui/x-charts-vendor@7.20.0":
  version "7.20.0"
  resolved "https://registry.npmjs.org/@mui/x-charts-vendor/-/x-charts-vendor-7.20.0.tgz"
  integrity sha512-pzlh7z/7KKs5o0Kk0oPcB+sY0+Dg7Q7RzqQowDQjpy5Slz6qqGsgOB5YUzn0L+2yRmvASc4Pe0914Ao3tMBogg==
  dependencies:
    "@babel/runtime" "^7.25.7"
    "@types/d3-color" "^3.1.3"
    "@types/d3-delaunay" "^6.0.4"
    "@types/d3-interpolate" "^3.0.4"
    "@types/d3-scale" "^4.0.8"
    "@types/d3-shape" "^3.1.6"
    "@types/d3-time" "^3.0.3"
    d3-color "^3.1.0"
    d3-delaunay "^6.0.4"
    d3-interpolate "^3.0.1"
    d3-scale "^4.0.2"
    d3-shape "^3.2.0"
    d3-time "^3.1.0"
    delaunator "^5.0.1"
    robust-predicates "^3.0.2"

"@mui/x-charts@^7.7.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@mui/x-charts/-/x-charts-7.27.1.tgz"
  integrity sha512-9z7fopitKjazY+p+sI2Z0zpip5zq3GYBC0hDuzxFUMvH582/FX1ZP6g1Wub0oetQReIMciL+rqU4agmRucvanw==
  dependencies:
    "@babel/runtime" "^7.25.7"
    "@mui/utils" "^5.16.6 || ^6.0.0"
    "@mui/x-charts-vendor" "7.20.0"
    "@mui/x-internals" "7.26.0"
    "@react-spring/rafz" "^9.7.5"
    "@react-spring/web" "^9.7.5"
    clsx "^2.1.1"
    prop-types "^15.8.1"

"@mui/x-internals@7.26.0":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@mui/x-internals/-/x-internals-7.26.0.tgz"
  integrity sha512-VxTCYQcZ02d3190pdvys2TDg9pgbvewAVakEopiOgReKAUhLdRlgGJHcOA/eAuGLyK1YIo26A6Ow6ZKlSRLwMg==
  dependencies:
    "@babel/runtime" "^7.25.7"
    "@mui/utils" "^5.16.6 || ^6.0.0"

"@napi-rs/canvas-win32-x64-msvc@0.1.68":
  version "0.1.68"
  resolved "https://registry.npmjs.org/@napi-rs/canvas-win32-x64-msvc/-/canvas-win32-x64-msvc-0.1.68.tgz"
  integrity sha512-Fc5Dez23u0FoSATurT6/w1oMytiRnKWEinHivdMvXpge6nG4YvhrASrtqMk8dGJMVQpHr8QJYF45rOrx2YU2Aw==

"@napi-rs/canvas@^0.1.65":
  version "0.1.68"
  resolved "https://registry.npmjs.org/@napi-rs/canvas/-/canvas-0.1.68.tgz"
  integrity sha512-LQESrePLEBLvhuFkXx9jjBXRC2ClYsO5mqQ1m/puth5z9SOuM3N/B3vDuqnC3RJFktDktyK9khGvo7dTkqO9uQ==
  optionalDependencies:
    "@napi-rs/canvas-android-arm64" "0.1.68"
    "@napi-rs/canvas-darwin-arm64" "0.1.68"
    "@napi-rs/canvas-darwin-x64" "0.1.68"
    "@napi-rs/canvas-linux-arm-gnueabihf" "0.1.68"
    "@napi-rs/canvas-linux-arm64-gnu" "0.1.68"
    "@napi-rs/canvas-linux-arm64-musl" "0.1.68"
    "@napi-rs/canvas-linux-riscv64-gnu" "0.1.68"
    "@napi-rs/canvas-linux-x64-gnu" "0.1.68"
    "@napi-rs/canvas-linux-x64-musl" "0.1.68"
    "@napi-rs/canvas-win32-x64-msvc" "0.1.68"

"@next/env@14.2.24":
  version "14.2.24"
  resolved "https://registry.npmjs.org/@next/env/-/env-14.2.24.tgz"
  integrity sha512-LAm0Is2KHTNT6IT16lxT+suD0u+VVfYNQqM+EJTKuFRRuY2z+zj01kueWXPCxbMBDt0B5vONYzabHGUNbZYAhA==

"@next/eslint-plugin-next@14.0.3":
  version "14.0.3"
  resolved "https://registry.npmjs.org/@next/eslint-plugin-next/-/eslint-plugin-next-14.0.3.tgz"
  integrity sha512-j4K0n+DcmQYCVnSAM+UByTVfIHnYQy2ODozfQP+4RdwtRDfobrIvKq1K4Exb2koJ79HSSa7s6B2SA8T/1YR3RA==
  dependencies:
    glob "7.1.7"

"@next/swc-win32-x64-msvc@14.2.24":
  version "14.2.24"
  resolved "https://registry.npmjs.org/@next/swc-win32-x64-msvc/-/swc-win32-x64-msvc-14.2.24.tgz"
  integrity sha512-cXcJ2+x0fXQ2CntaE00d7uUH+u1Bfp/E0HsNQH79YiLaZE5Rbm7dZzyAYccn3uICM7mw+DxoMqEfGXZtF4Fgaw==

"@nextui-org/accordion@2.2.7":
  version "2.2.7"
  resolved "https://registry.npmjs.org/@nextui-org/accordion/-/accordion-2.2.7.tgz"
  integrity sha512-jdobOwUxSi617m+LpxHFzg64UhDuOfDJI2CMk3MP+b2WBJ7SNW4hmN2NW5Scx5JiY+kyBGmlxJ4Y++jZpZgQjQ==
  dependencies:
    "@nextui-org/aria-utils" "2.2.7"
    "@nextui-org/divider" "2.2.5"
    "@nextui-org/dom-animation" "2.1.1"
    "@nextui-org/framer-utils" "2.1.6"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-icons" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-aria-accordion" "2.2.2"
    "@react-aria/button" "3.11.0"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/utils" "3.26.0"
    "@react-stately/tree" "3.8.6"
    "@react-types/accordion" "3.0.0-alpha.25"
    "@react-types/shared" "3.26.0"

"@nextui-org/alert@2.2.9":
  version "2.2.9"
  resolved "https://registry.npmjs.org/@nextui-org/alert/-/alert-2.2.9.tgz"
  integrity sha512-SjMZewEqknx/jqmMcyQdbeo6RFg40+A3b1lGjnj/fdkiJozQoTesiOslzDsacqiSgvso2F+8u1emC2tFBAU3hw==
  dependencies:
    "@nextui-org/button" "2.2.9"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-icons" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"
    "@react-aria/utils" "3.26.0"
    "@react-stately/utils" "3.10.5"

"@nextui-org/aria-utils@2.2.7":
  version "2.2.7"
  resolved "https://registry.npmjs.org/@nextui-org/aria-utils/-/aria-utils-2.2.7.tgz"
  integrity sha512-QgMZ8fii6BCI/+ZIkgXgkm/gMNQ92pQJn83q90fBT6DF+6j4hsCpJwLNCF5mIJkX/cQ/4bHDsDaj7w1OzkhQNg==
  dependencies:
    "@nextui-org/react-rsc-utils" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/system" "2.4.6"
    "@react-aria/utils" "3.26.0"
    "@react-stately/collections" "3.12.0"
    "@react-stately/overlays" "3.6.12"
    "@react-types/overlays" "3.8.11"
    "@react-types/shared" "3.26.0"

"@nextui-org/autocomplete@2.3.9":
  version "2.3.9"
  resolved "https://registry.npmjs.org/@nextui-org/autocomplete/-/autocomplete-2.3.9.tgz"
  integrity sha512-1AizOvL8lERoWjm8WiA0NPJWB3h0gqYlbV/qGZeacac5356hb8cNzWUlxGzr9bNkhn9slIoEUyGMgtYeKq7ptg==
  dependencies:
    "@nextui-org/aria-utils" "2.2.7"
    "@nextui-org/button" "2.2.9"
    "@nextui-org/form" "2.1.8"
    "@nextui-org/input" "2.4.8"
    "@nextui-org/listbox" "2.3.9"
    "@nextui-org/popover" "2.3.9"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/scroll-shadow" "2.3.5"
    "@nextui-org/shared-icons" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/spinner" "2.2.6"
    "@nextui-org/use-aria-button" "2.2.4"
    "@nextui-org/use-safe-layout-effect" "2.1.1"
    "@react-aria/combobox" "3.11.0"
    "@react-aria/focus" "3.19.0"
    "@react-aria/i18n" "3.12.4"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/utils" "3.26.0"
    "@react-aria/visually-hidden" "3.8.18"
    "@react-stately/combobox" "3.10.1"
    "@react-types/combobox" "3.13.1"
    "@react-types/shared" "3.26.0"

"@nextui-org/avatar@2.2.6":
  version "2.2.6"
  resolved "https://registry.npmjs.org/@nextui-org/avatar/-/avatar-2.2.6.tgz"
  integrity sha512-QRNCAMXnSZrFJYKo78lzRPiAPRq5pn1LIHUVvX/mCRiTvbu1FXrMakAvOWz/n1X1mLndnrfQMRNgmtC8YlHIdg==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-image" "2.1.2"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/utils" "3.26.0"

"@nextui-org/badge@2.2.5":
  version "2.2.5"
  resolved "https://registry.npmjs.org/@nextui-org/badge/-/badge-2.2.5.tgz"
  integrity sha512-8pLbuY+RVCzI/00CzNudc86BiuXByPFz2yHh00djKvZAXbT0lfjvswClJxSC2FjUXlod+NtE+eHmlhSMo3gmpw==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"

"@nextui-org/breadcrumbs@2.2.6":
  version "2.2.6"
  resolved "https://registry.npmjs.org/@nextui-org/breadcrumbs/-/breadcrumbs-2.2.6.tgz"
  integrity sha512-TlAUSiIClmm02tJqOvtwySpKDOENduXCXkKzCbmSaqEFhziHnhyE0eM8IVEprBoK6z1VP+sUrX6C2gZ871KUSw==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-icons" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"
    "@react-aria/breadcrumbs" "3.5.19"
    "@react-aria/focus" "3.19.0"
    "@react-aria/utils" "3.26.0"
    "@react-types/breadcrumbs" "3.7.9"
    "@react-types/shared" "3.26.0"

"@nextui-org/button@2.2.9":
  version "2.2.9"
  resolved "https://registry.npmjs.org/@nextui-org/button/-/button-2.2.9.tgz"
  integrity sha512-RrfjAZHoc6nmaqoLj40M0Qj3tuDdv2BMGCgggyWklOi6lKwtOaADPvxEorDwY3GnN54Xej+9SWtUwE8Oc3SnOg==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/ripple" "2.2.7"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/spinner" "2.2.6"
    "@nextui-org/use-aria-button" "2.2.4"
    "@react-aria/button" "3.11.0"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/utils" "3.26.0"
    "@react-types/button" "3.10.1"
    "@react-types/shared" "3.26.0"

"@nextui-org/calendar@2.2.9":
  version "2.2.9"
  resolved "https://registry.npmjs.org/@nextui-org/calendar/-/calendar-2.2.9.tgz"
  integrity sha512-tx1401HLnwadoDHNkmEIZNeAw9uYW6KsgIRRQnXTNVstBXdMmPWjoMBj8fkQqF55+U58k6a+w3N4tTpgRGOpaQ==
  dependencies:
    "@internationalized/date" "3.6.0"
    "@nextui-org/button" "2.2.9"
    "@nextui-org/dom-animation" "2.1.1"
    "@nextui-org/framer-utils" "2.1.6"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-icons" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-aria-button" "2.2.4"
    "@react-aria/calendar" "3.6.0"
    "@react-aria/focus" "3.19.0"
    "@react-aria/i18n" "3.12.4"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/utils" "3.26.0"
    "@react-aria/visually-hidden" "3.8.18"
    "@react-stately/calendar" "3.6.0"
    "@react-stately/utils" "3.10.5"
    "@react-types/button" "3.10.1"
    "@react-types/calendar" "3.5.0"
    "@react-types/shared" "3.26.0"
    "@types/lodash.debounce" "^4.0.7"
    scroll-into-view-if-needed "3.0.10"

"@nextui-org/card@2.2.9":
  version "2.2.9"
  resolved "https://registry.npmjs.org/@nextui-org/card/-/card-2.2.9.tgz"
  integrity sha512-Ltvb5Uy4wwkBJj3QvVQmoB6PwLYUNSoWAFo2xxu7LUHKWcETYI0YbUIuwL2nFU2xfJYeBTGjXGQO1ffBsowrtQ==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/ripple" "2.2.7"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-aria-button" "2.2.4"
    "@react-aria/button" "3.11.0"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/utils" "3.26.0"
    "@react-types/shared" "3.26.0"

"@nextui-org/checkbox@2.3.8":
  version "2.3.8"
  resolved "https://registry.npmjs.org/@nextui-org/checkbox/-/checkbox-2.3.8.tgz"
  integrity sha512-T5+AhzQfbg53qZnPn5rgMcJ7T5rnvSGYTx17wHWtdF9Q4QflZOmLGoxqoTWbTVpM4XzUUPyi7KVSKZScWdBDAA==
  dependencies:
    "@nextui-org/form" "2.1.8"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-callback-ref" "2.1.1"
    "@nextui-org/use-safe-layout-effect" "2.1.1"
    "@react-aria/checkbox" "3.15.0"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/utils" "3.26.0"
    "@react-aria/visually-hidden" "3.8.18"
    "@react-stately/checkbox" "3.6.10"
    "@react-stately/toggle" "3.8.0"
    "@react-types/checkbox" "3.9.0"
    "@react-types/shared" "3.26.0"

"@nextui-org/chip@2.2.6":
  version "2.2.6"
  resolved "https://registry.npmjs.org/@nextui-org/chip/-/chip-2.2.6.tgz"
  integrity sha512-HrSYagbrD4u4nblsNMIu7WGnDj9A8YnYCt30tasJmNSyydUVHFkxKOc3S8k+VU3BHPxeENxeBT7w0OlYoKbFIQ==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-icons" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/utils" "3.26.0"
    "@react-types/checkbox" "3.9.0"

"@nextui-org/code@2.2.6":
  version "2.2.6"
  resolved "https://registry.npmjs.org/@nextui-org/code/-/code-2.2.6.tgz"
  integrity sha512-8qvAywIKAVh1thy/YHNwqH2xjTcwPiOWwNdKqvJMSk0CNtLHYJmDK8i2vmKZTM3zfB08Q/G94H0Wf+YsyrZdDg==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/system-rsc" "2.3.5"

"@nextui-org/date-input@2.3.8":
  version "2.3.8"
  resolved "https://registry.npmjs.org/@nextui-org/date-input/-/date-input-2.3.8.tgz"
  integrity sha512-phj0Y8F/GpsKjKSiratFwh7HDzmMsIf6G2L2ljgWqA79PvP+RYf/ogEfaMIq1knF8OlssMo5nsFFJNsNB+xKGg==
  dependencies:
    "@internationalized/date" "3.6.0"
    "@nextui-org/form" "2.1.8"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@react-aria/datepicker" "3.12.0"
    "@react-aria/i18n" "3.12.4"
    "@react-aria/utils" "3.26.0"
    "@react-stately/datepicker" "3.11.0"
    "@react-types/datepicker" "3.9.0"
    "@react-types/shared" "3.26.0"

"@nextui-org/date-picker@2.3.9":
  version "2.3.9"
  resolved "https://registry.npmjs.org/@nextui-org/date-picker/-/date-picker-2.3.9.tgz"
  integrity sha512-RzdVTl/tulTyE5fwGkQfn0is5hsTkPPRJFJZXMqYeci85uhpD+bCreWnTXrGFIXcqUo0ZBJWx3EdtBJZnGp4xQ==
  dependencies:
    "@internationalized/date" "3.6.0"
    "@nextui-org/aria-utils" "2.2.7"
    "@nextui-org/button" "2.2.9"
    "@nextui-org/calendar" "2.2.9"
    "@nextui-org/date-input" "2.3.8"
    "@nextui-org/form" "2.1.8"
    "@nextui-org/popover" "2.3.9"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-icons" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"
    "@react-aria/datepicker" "3.12.0"
    "@react-aria/i18n" "3.12.4"
    "@react-aria/utils" "3.26.0"
    "@react-stately/datepicker" "3.11.0"
    "@react-stately/overlays" "3.6.12"
    "@react-stately/utils" "3.10.5"
    "@react-types/datepicker" "3.9.0"
    "@react-types/shared" "3.26.0"

"@nextui-org/divider@2.2.5":
  version "2.2.5"
  resolved "https://registry.npmjs.org/@nextui-org/divider/-/divider-2.2.5.tgz"
  integrity sha512-OB8b3CU4nQ5ARIGL48izhzrAHR0mnwws+Kd5LqRCZ/1R9uRMqsq7L0gpG9FkuV2jf2FuA7xa/GLOLKbIl4CEww==
  dependencies:
    "@nextui-org/react-rsc-utils" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/system-rsc" "2.3.5"
    "@react-types/shared" "3.26.0"

"@nextui-org/dom-animation@2.1.1":
  version "2.1.1"
  resolved "https://registry.npmjs.org/@nextui-org/dom-animation/-/dom-animation-2.1.1.tgz"
  integrity sha512-xLrVNf1EV9zyyZjk6j3RptOvnga1WUCbMpDgJLQHp+oYwxTfBy0SkXHuN5pRdcR0XpR/IqRBDIobMdZI0iyQyg==

"@nextui-org/drawer@2.2.7":
  version "2.2.7"
  resolved "https://registry.npmjs.org/@nextui-org/drawer/-/drawer-2.2.7.tgz"
  integrity sha512-a1Sr3sSjOZD0SiXDYSySKkOelTyCYExPvUsIckzjF5A3TNlBw4KFKnJzaXvabC3SNRy6/Ocq7oqz6VRv37wxQg==
  dependencies:
    "@nextui-org/framer-utils" "2.1.6"
    "@nextui-org/modal" "2.2.7"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"

"@nextui-org/dropdown@2.3.9":
  version "2.3.9"
  resolved "https://registry.npmjs.org/@nextui-org/dropdown/-/dropdown-2.3.9.tgz"
  integrity sha512-ElZxiP+nG0CKC+tm6LMZX42cRWXQ0LLjWBZXymupPsEH3XcQpCF9GWb9efJ2hh+qGROg7i0bnFH7P0GTyCyNBA==
  dependencies:
    "@nextui-org/aria-utils" "2.2.7"
    "@nextui-org/menu" "2.2.9"
    "@nextui-org/popover" "2.3.9"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@react-aria/focus" "3.19.0"
    "@react-aria/menu" "3.16.0"
    "@react-aria/utils" "3.26.0"
    "@react-stately/menu" "3.9.0"
    "@react-types/menu" "3.9.13"

"@nextui-org/form@2.1.8":
  version "2.1.8"
  resolved "https://registry.npmjs.org/@nextui-org/form/-/form-2.1.8.tgz"
  integrity sha512-Xn/dUO5zDG7zukbql1MDYh4Xwe1vnIVMRTHgckbkBtXXVNqgoTU09TTfy8WOJ0pMDX4GrZSBAZ86o37O+IHbaA==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/system" "2.4.6"
    "@nextui-org/theme" "2.4.5"
    "@react-aria/utils" "3.26.0"
    "@react-stately/form" "3.1.0"
    "@react-types/form" "3.7.8"
    "@react-types/shared" "3.26.0"

"@nextui-org/framer-utils@2.1.6":
  version "2.1.6"
  resolved "https://registry.npmjs.org/@nextui-org/framer-utils/-/framer-utils-2.1.6.tgz"
  integrity sha512-b+BxKFox8j9rNAaL+CRe2ZMb1/SKjz9Kl2eLjDSsq3q82K/Hg7lEjlpgE8cu41wIGjH1unQxtP+btiJgl067Ow==
  dependencies:
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/system" "2.4.6"
    "@nextui-org/use-measure" "2.1.1"

"@nextui-org/image@2.2.5":
  version "2.2.5"
  resolved "https://registry.npmjs.org/@nextui-org/image/-/image-2.2.5.tgz"
  integrity sha512-A6DnEqG+/cMrfvqFKKJIdGD7gD88tVkqGxRkfysVMJJR96sDIYCJlP1jsAEtYKh4PfhmtJWclUvY/x9fMw0H1w==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-image" "2.1.2"

"@nextui-org/input-otp@2.1.8":
  version "2.1.8"
  resolved "https://registry.npmjs.org/@nextui-org/input-otp/-/input-otp-2.1.8.tgz"
  integrity sha512-J5Pz0aSfWD+2cSgLTKQamCNF/qHILIj8L0lY3t1R/sgK1ApN3kDNcUGnVm6EDh+dOXITKpCfnsCQw834nxZhsg==
  dependencies:
    "@nextui-org/form" "2.1.8"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@react-aria/focus" "3.19.0"
    "@react-aria/form" "3.0.11"
    "@react-aria/utils" "3.26.0"
    "@react-stately/form" "3.1.0"
    "@react-stately/utils" "3.10.5"
    "@react-types/textfield" "3.10.0"
    input-otp "1.4.1"

"@nextui-org/input@2.4.8":
  version "2.4.8"
  resolved "https://registry.npmjs.org/@nextui-org/input/-/input-2.4.8.tgz"
  integrity sha512-wfkjyl7vRqT3HDXeybhfZ+IAz+Z02U5EiuWPpc9NbdwhJ/LpDRDa6fYcTDr/6j6MiyrEZsM24CtZZKAKBVBquQ==
  dependencies:
    "@nextui-org/form" "2.1.8"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-icons" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-safe-layout-effect" "2.1.1"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/textfield" "3.15.0"
    "@react-aria/utils" "3.26.0"
    "@react-stately/utils" "3.10.5"
    "@react-types/shared" "3.26.0"
    "@react-types/textfield" "3.10.0"
    react-textarea-autosize "^8.5.3"

"@nextui-org/kbd@2.2.6":
  version "2.2.6"
  resolved "https://registry.npmjs.org/@nextui-org/kbd/-/kbd-2.2.6.tgz"
  integrity sha512-IwzvvwYLMbhyqX5PjEZyDBO4iNEHY6Nek4ZrVR+Z2dOSj/oZXHWiabNDrvOcGKgUBE6xc95Fi1jVubE9b5ueuA==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/system-rsc" "2.3.5"
    "@react-aria/utils" "3.26.0"

"@nextui-org/link@2.2.7":
  version "2.2.7"
  resolved "https://registry.npmjs.org/@nextui-org/link/-/link-2.2.7.tgz"
  integrity sha512-SAeBBCUtdaKtHfZgRD6OH0De/+cKUEuThiErSuFW+sNm/y8m3cUhQH8UqVBPu6HwmqVTEjvZzp/4uhG6lcSZjA==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-icons" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-aria-link" "2.2.5"
    "@react-aria/focus" "3.19.0"
    "@react-aria/link" "3.7.7"
    "@react-aria/utils" "3.26.0"
    "@react-types/link" "3.5.9"

"@nextui-org/listbox@2.3.9":
  version "2.3.9"
  resolved "https://registry.npmjs.org/@nextui-org/listbox/-/listbox-2.3.9.tgz"
  integrity sha512-iGJ8xwkXf8K7chk1iZgC05KGpHiWJXY1dnV7ytIJ7yu4BbsRIHb0QknK5j8A74YeGpouJQ9+jsmCERmySxlqlg==
  dependencies:
    "@nextui-org/aria-utils" "2.2.7"
    "@nextui-org/divider" "2.2.5"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-is-mobile" "2.2.2"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/listbox" "3.13.6"
    "@react-aria/utils" "3.26.0"
    "@react-stately/list" "3.11.1"
    "@react-types/menu" "3.9.13"
    "@react-types/shared" "3.26.0"
    "@tanstack/react-virtual" "3.11.2"

"@nextui-org/menu@2.2.9":
  version "2.2.9"
  resolved "https://registry.npmjs.org/@nextui-org/menu/-/menu-2.2.9.tgz"
  integrity sha512-Fztvi3GRYl5a5FO/0LRzcAdnw8Yeq6NX8yLQh8XmwkWCrH0S6nTn69CP/j+EMWQR6G2UK5AbNDmX1Sx9aTQdHQ==
  dependencies:
    "@nextui-org/aria-utils" "2.2.7"
    "@nextui-org/divider" "2.2.5"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-is-mobile" "2.2.2"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/menu" "3.16.0"
    "@react-aria/utils" "3.26.0"
    "@react-stately/menu" "3.9.0"
    "@react-stately/tree" "3.8.6"
    "@react-types/menu" "3.9.13"
    "@react-types/shared" "3.26.0"

"@nextui-org/modal@2.2.7":
  version "2.2.7"
  resolved "https://registry.npmjs.org/@nextui-org/modal/-/modal-2.2.7.tgz"
  integrity sha512-xxk6B+5s8//qYI4waLjdWoJFwR6Zqym/VHFKkuZAMpNABgTB0FCK022iUdOIP2F2epG69un8zJF0qwMBJF8XAA==
  dependencies:
    "@nextui-org/dom-animation" "2.1.1"
    "@nextui-org/framer-utils" "2.1.6"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-icons" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-aria-button" "2.2.4"
    "@nextui-org/use-aria-modal-overlay" "2.2.3"
    "@nextui-org/use-disclosure" "2.2.2"
    "@nextui-org/use-draggable" "2.1.2"
    "@react-aria/dialog" "3.5.20"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/overlays" "3.24.0"
    "@react-aria/utils" "3.26.0"
    "@react-stately/overlays" "3.6.12"
    "@react-types/overlays" "3.8.11"

"@nextui-org/navbar@2.2.8":
  version "2.2.8"
  resolved "https://registry.npmjs.org/@nextui-org/navbar/-/navbar-2.2.8.tgz"
  integrity sha512-XutioQ75jonZk6TBtjFdV6N3eLe8y85tetjOdOg6X3mKTPZlQuBb+rtb6pVNOOvcuQ7zKigWIq2ammvF9VNKaQ==
  dependencies:
    "@nextui-org/dom-animation" "2.1.1"
    "@nextui-org/framer-utils" "2.1.6"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-scroll-position" "2.1.1"
    "@react-aria/button" "3.11.0"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/overlays" "3.24.0"
    "@react-aria/utils" "3.26.0"
    "@react-stately/toggle" "3.8.0"
    "@react-stately/utils" "3.10.5"

"@nextui-org/pagination@2.2.8":
  version "2.2.8"
  resolved "https://registry.npmjs.org/@nextui-org/pagination/-/pagination-2.2.8.tgz"
  integrity sha512-sZcriQq/ssOItX3r54tysnItjcb7dw392BNulJxrMMXi6FA6sUGImpJF1jsbtYJvaq346IoZvMrcrba8PXEk0g==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-icons" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-intersection-observer" "2.2.2"
    "@nextui-org/use-pagination" "2.2.3"
    "@react-aria/focus" "3.19.0"
    "@react-aria/i18n" "3.12.4"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/utils" "3.26.0"
    scroll-into-view-if-needed "3.0.10"

"@nextui-org/popover@2.3.9":
  version "2.3.9"
  resolved "https://registry.npmjs.org/@nextui-org/popover/-/popover-2.3.9.tgz"
  integrity sha512-glLYKlFJ4EkFrNMBC3ediFPpQwKzaFlzKoaMum2G3HUtmC4d1HLTSOQJOd2scUzZxD3/K9dp1XHYbEcCnCrYpQ==
  dependencies:
    "@nextui-org/aria-utils" "2.2.7"
    "@nextui-org/button" "2.2.9"
    "@nextui-org/dom-animation" "2.1.1"
    "@nextui-org/framer-utils" "2.1.6"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-aria-button" "2.2.4"
    "@nextui-org/use-safe-layout-effect" "2.1.1"
    "@react-aria/dialog" "3.5.20"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/overlays" "3.24.0"
    "@react-aria/utils" "3.26.0"
    "@react-stately/overlays" "3.6.12"
    "@react-types/button" "3.10.1"
    "@react-types/overlays" "3.8.11"

"@nextui-org/progress@^2.0.33", "@nextui-org/progress@2.2.6":
  version "2.2.6"
  resolved "https://registry.npmjs.org/@nextui-org/progress/-/progress-2.2.6.tgz"
  integrity sha512-FTicOncNcXKpt9avxQWWlVATvhABKVMBgsB81SozFXRcn8QsFntjdMp0l3688DJKBY0GxT+yl/S/by0TwY1Z1A==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-is-mounted" "2.1.1"
    "@react-aria/i18n" "3.12.4"
    "@react-aria/progress" "3.4.18"
    "@react-aria/utils" "3.26.0"
    "@react-types/progress" "3.5.8"

"@nextui-org/radio@2.3.8":
  version "2.3.8"
  resolved "https://registry.npmjs.org/@nextui-org/radio/-/radio-2.3.8.tgz"
  integrity sha512-ntwjpQ/WT8zQ3Fw5io65VeH2Q68LOgZ4lII7a6x35NDa7Eda1vlYroMAw/vxK8iyZYlUBSJdsoj2FU/10hBPmg==
  dependencies:
    "@nextui-org/form" "2.1.8"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/radio" "3.10.10"
    "@react-aria/utils" "3.26.0"
    "@react-aria/visually-hidden" "3.8.18"
    "@react-stately/radio" "3.10.9"
    "@react-types/radio" "3.8.5"
    "@react-types/shared" "3.26.0"

"@nextui-org/react-rsc-utils@2.1.1":
  version "2.1.1"
  resolved "https://registry.npmjs.org/@nextui-org/react-rsc-utils/-/react-rsc-utils-2.1.1.tgz"
  integrity sha512-9uKH1XkeomTGaswqlGKt0V0ooUev8mPXtKJolR+6MnpvBUrkqngw1gUGF0bq/EcCCkks2+VOHXZqFT6x9hGkQQ==

"@nextui-org/react-utils@2.1.3":
  version "2.1.3"
  resolved "https://registry.npmjs.org/@nextui-org/react-utils/-/react-utils-2.1.3.tgz"
  integrity sha512-o61fOS+S8p3KtgLLN7ub5gR0y7l517l9eZXJabUdnVcZzZjTqEijWjzjIIIyAtYAlL4d+WTXEOROuc32sCmbqw==
  dependencies:
    "@nextui-org/react-rsc-utils" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"

"@nextui-org/react@^2.4.6":
  version "2.6.11"
  resolved "https://registry.npmjs.org/@nextui-org/react/-/react-2.6.11.tgz"
  integrity sha512-MOkBMWI+1nHB6A8YLXakdXrNRFvy5whjFJB1FthwqbP8pVEeksS1e29AbfEFkrzLc5zjN7i24wGNSJ8DKMt9WQ==
  dependencies:
    "@nextui-org/accordion" "2.2.7"
    "@nextui-org/alert" "2.2.9"
    "@nextui-org/autocomplete" "2.3.9"
    "@nextui-org/avatar" "2.2.6"
    "@nextui-org/badge" "2.2.5"
    "@nextui-org/breadcrumbs" "2.2.6"
    "@nextui-org/button" "2.2.9"
    "@nextui-org/calendar" "2.2.9"
    "@nextui-org/card" "2.2.9"
    "@nextui-org/checkbox" "2.3.8"
    "@nextui-org/chip" "2.2.6"
    "@nextui-org/code" "2.2.6"
    "@nextui-org/date-input" "2.3.8"
    "@nextui-org/date-picker" "2.3.9"
    "@nextui-org/divider" "2.2.5"
    "@nextui-org/drawer" "2.2.7"
    "@nextui-org/dropdown" "2.3.9"
    "@nextui-org/form" "2.1.8"
    "@nextui-org/framer-utils" "2.1.6"
    "@nextui-org/image" "2.2.5"
    "@nextui-org/input" "2.4.8"
    "@nextui-org/input-otp" "2.1.8"
    "@nextui-org/kbd" "2.2.6"
    "@nextui-org/link" "2.2.7"
    "@nextui-org/listbox" "2.3.9"
    "@nextui-org/menu" "2.2.9"
    "@nextui-org/modal" "2.2.7"
    "@nextui-org/navbar" "2.2.8"
    "@nextui-org/pagination" "2.2.8"
    "@nextui-org/popover" "2.3.9"
    "@nextui-org/progress" "2.2.6"
    "@nextui-org/radio" "2.3.8"
    "@nextui-org/ripple" "2.2.7"
    "@nextui-org/scroll-shadow" "2.3.5"
    "@nextui-org/select" "2.4.9"
    "@nextui-org/skeleton" "2.2.5"
    "@nextui-org/slider" "2.4.7"
    "@nextui-org/snippet" "2.2.10"
    "@nextui-org/spacer" "2.2.6"
    "@nextui-org/spinner" "2.2.6"
    "@nextui-org/switch" "2.2.8"
    "@nextui-org/system" "2.4.6"
    "@nextui-org/table" "2.2.8"
    "@nextui-org/tabs" "2.2.7"
    "@nextui-org/theme" "2.4.5"
    "@nextui-org/tooltip" "2.2.7"
    "@nextui-org/user" "2.2.6"
    "@react-aria/visually-hidden" "3.8.18"

"@nextui-org/ripple@2.2.7":
  version "2.2.7"
  resolved "https://registry.npmjs.org/@nextui-org/ripple/-/ripple-2.2.7.tgz"
  integrity sha512-cphzlvCjdROh1JWQhO/wAsmBdlU9kv/UA2YRQS4viaWcA3zO+qOZVZ9/YZMan6LBlOLENCaE9CtV2qlzFtVpEg==
  dependencies:
    "@nextui-org/dom-animation" "2.1.1"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"

"@nextui-org/scroll-shadow@2.3.5":
  version "2.3.5"
  resolved "https://registry.npmjs.org/@nextui-org/scroll-shadow/-/scroll-shadow-2.3.5.tgz"
  integrity sha512-2H5qro6RHcWo6ZfcG2hHZHsR1LrV3FMZP5Lkc9ZwJdWPg4dXY4erGRE4U+B7me6efj5tBOFmZkIpxVUyMBLtZg==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-data-scroll-overflow" "2.2.2"

"@nextui-org/select@2.4.9":
  version "2.4.9"
  resolved "https://registry.npmjs.org/@nextui-org/select/-/select-2.4.9.tgz"
  integrity sha512-R8HHKDH7dA4Dv73Pl80X7qfqdyl+Fw4gi/9bmyby0QJG8LN2zu51xyjjKphmWVkAiE3O35BRVw7vMptHnWFUgQ==
  dependencies:
    "@nextui-org/aria-utils" "2.2.7"
    "@nextui-org/form" "2.1.8"
    "@nextui-org/listbox" "2.3.9"
    "@nextui-org/popover" "2.3.9"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/scroll-shadow" "2.3.5"
    "@nextui-org/shared-icons" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/spinner" "2.2.6"
    "@nextui-org/use-aria-button" "2.2.4"
    "@nextui-org/use-aria-multiselect" "2.4.3"
    "@nextui-org/use-safe-layout-effect" "2.1.1"
    "@react-aria/focus" "3.19.0"
    "@react-aria/form" "3.0.11"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/utils" "3.26.0"
    "@react-aria/visually-hidden" "3.8.18"
    "@react-types/shared" "3.26.0"
    "@tanstack/react-virtual" "3.11.2"

"@nextui-org/shared-icons@2.1.1":
  version "2.1.1"
  resolved "https://registry.npmjs.org/@nextui-org/shared-icons/-/shared-icons-2.1.1.tgz"
  integrity sha512-mkiTpFJnCzB2M8Dl7IwXVzDKKq9ZW2WC0DaQRs1eWgqboRCP8DDde+MJZq331hC7pfH8BC/4rxXsKECrOUUwCg==

"@nextui-org/shared-utils@2.1.2":
  version "2.1.2"
  resolved "https://registry.npmjs.org/@nextui-org/shared-utils/-/shared-utils-2.1.2.tgz"
  integrity sha512-5n0D+AGB4P9lMD1TxwtdRSuSY0cWgyXKO9mMU11Xl3zoHNiAz/SbCSTc4VBJdQJ7Y3qgNXvZICzf08+bnjjqqA==

"@nextui-org/skeleton@2.2.5":
  version "2.2.5"
  resolved "https://registry.npmjs.org/@nextui-org/skeleton/-/skeleton-2.2.5.tgz"
  integrity sha512-CK1O9dqS0xPW3o1SIekEEOjSosJkXNzU0Zd538Nn1XhY1RjNuIPchpY9Pv5YZr2QSKy0zkwPQt/NalwErke0Jg==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"

"@nextui-org/slider@2.4.7":
  version "2.4.7"
  resolved "https://registry.npmjs.org/@nextui-org/slider/-/slider-2.4.7.tgz"
  integrity sha512-/RnjnmAPvssebhtElG+ZI8CCot2dEBcEjw7LrHfmVnJOd5jgceMtnXhdJSppQuLvcC4fPpkhd6dY86IezOZwfw==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/tooltip" "2.2.7"
    "@react-aria/focus" "3.19.0"
    "@react-aria/i18n" "3.12.4"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/slider" "3.7.14"
    "@react-aria/utils" "3.26.0"
    "@react-aria/visually-hidden" "3.8.18"
    "@react-stately/slider" "3.6.0"

"@nextui-org/snippet@2.2.10":
  version "2.2.10"
  resolved "https://registry.npmjs.org/@nextui-org/snippet/-/snippet-2.2.10.tgz"
  integrity sha512-mVjf8muq4TX2PlESN7EeHgFmjuz7PNhrKFP+fb8Lj9J6wvUIUDm5ENv9bs72cRsK+zse6OUNE4JF1er6HllKug==
  dependencies:
    "@nextui-org/button" "2.2.9"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-icons" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/tooltip" "2.2.7"
    "@nextui-org/use-clipboard" "2.1.2"
    "@react-aria/focus" "3.19.0"
    "@react-aria/utils" "3.26.0"

"@nextui-org/spacer@2.2.6":
  version "2.2.6"
  resolved "https://registry.npmjs.org/@nextui-org/spacer/-/spacer-2.2.6.tgz"
  integrity sha512-1qYtZ6xICfSrFV0MMB/nUH1K2X9mHzIikrjC/okzyzWywibsVNbyRfu5vObVClYlVGY0r4M4+7fpV2QV1tKRGw==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/system-rsc" "2.3.5"

"@nextui-org/spinner@2.2.6":
  version "2.2.6"
  resolved "https://registry.npmjs.org/@nextui-org/spinner/-/spinner-2.2.6.tgz"
  integrity sha512-0V0H8jVpgRolgLnCuKDbrQCSK0VFPAZYiyGOE1+dfyIezpta+Nglh+uEl2sEFNh6B9Z8mARB8YEpRnTcA0ePDw==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/system-rsc" "2.3.5"

"@nextui-org/switch@2.2.8":
  version "2.2.8"
  resolved "https://registry.npmjs.org/@nextui-org/switch/-/switch-2.2.8.tgz"
  integrity sha512-wk9qQSOfUEtmdWR1omKjmEYzgMjJhVizvfW6Z0rKOiMUuSud2d4xYnUmZhU22cv2WtoPV//kBjXkYD/E/t6rdg==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-safe-layout-effect" "2.1.1"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/switch" "3.6.10"
    "@react-aria/utils" "3.26.0"
    "@react-aria/visually-hidden" "3.8.18"
    "@react-stately/toggle" "3.8.0"
    "@react-types/shared" "3.26.0"

"@nextui-org/system-rsc@2.3.5":
  version "2.3.5"
  resolved "https://registry.npmjs.org/@nextui-org/system-rsc/-/system-rsc-2.3.5.tgz"
  integrity sha512-DpVLNV9LkeP1yDULFCXm2mxA9m4ygS7XYy3lwgcF9M1A8QAWB+ut+FcP+8a6va50oSHOqwvUwPDUslgXTPMBfQ==
  dependencies:
    "@react-types/shared" "3.26.0"
    clsx "^1.2.1"

"@nextui-org/system@>=2.4.0", "@nextui-org/system@2.4.6":
  version "2.4.6"
  resolved "https://registry.npmjs.org/@nextui-org/system/-/system-2.4.6.tgz"
  integrity sha512-6ujAriBZMfQ16n6M6Ad9g32KJUa1CzqIVaHN/tymadr/3m8hrr7xDw6z50pVjpCRq2PaaA1hT8Hx7EFU3f2z3Q==
  dependencies:
    "@internationalized/date" "3.6.0"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/system-rsc" "2.3.5"
    "@react-aria/i18n" "3.12.4"
    "@react-aria/overlays" "3.24.0"
    "@react-aria/utils" "3.26.0"
    "@react-stately/utils" "3.10.5"
    "@react-types/datepicker" "3.9.0"

"@nextui-org/table@2.2.8":
  version "2.2.8"
  resolved "https://registry.npmjs.org/@nextui-org/table/-/table-2.2.8.tgz"
  integrity sha512-XNM0/Ed7Re3BA1eHL31rzALea9hgsBwD0rMR2qB2SAl2e8KaV2o+4bzgYhpISAzHQtlG8IsXanxiuNDH8OPVyw==
  dependencies:
    "@nextui-org/checkbox" "2.3.8"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-icons" "2.1.1"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/spacer" "2.2.6"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/table" "3.16.0"
    "@react-aria/utils" "3.26.0"
    "@react-aria/visually-hidden" "3.8.18"
    "@react-stately/table" "3.13.0"
    "@react-stately/virtualizer" "4.2.0"
    "@react-types/grid" "3.2.10"
    "@react-types/table" "3.10.3"

"@nextui-org/tabs@2.2.7":
  version "2.2.7"
  resolved "https://registry.npmjs.org/@nextui-org/tabs/-/tabs-2.2.7.tgz"
  integrity sha512-EDPK0MOR4DPTfud9Khr5AikLbyEhHTlkGfazbOxg7wFaHysOnV5Y/E6UfvaN69kgIeT7NQcDFdaCKJ/AX1N7AA==
  dependencies:
    "@nextui-org/aria-utils" "2.2.7"
    "@nextui-org/framer-utils" "2.1.6"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-is-mounted" "2.1.1"
    "@nextui-org/use-update-effect" "2.1.1"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/tabs" "3.9.8"
    "@react-aria/utils" "3.26.0"
    "@react-stately/tabs" "3.7.0"
    "@react-types/shared" "3.26.0"
    "@react-types/tabs" "3.3.11"
    scroll-into-view-if-needed "3.0.10"

"@nextui-org/theme@>=2.4.0", "@nextui-org/theme@>=2.4.3", "@nextui-org/theme@2.4.5":
  version "2.4.5"
  resolved "https://registry.npmjs.org/@nextui-org/theme/-/theme-2.4.5.tgz"
  integrity sha512-c7Y17n+hBGiFedxMKfg7Qyv93iY5MteamLXV4Po4c1VF1qZJI6I+IKULFh3FxPWzAoz96r6NdYT7OLFjrAJdWg==
  dependencies:
    "@nextui-org/shared-utils" "2.1.2"
    clsx "^1.2.1"
    color "^4.2.3"
    color2k "^2.0.2"
    deepmerge "4.3.1"
    flat "^5.0.2"
    tailwind-merge "^2.5.2"
    tailwind-variants "^0.1.20"

"@nextui-org/tooltip@2.2.7":
  version "2.2.7"
  resolved "https://registry.npmjs.org/@nextui-org/tooltip/-/tooltip-2.2.7.tgz"
  integrity sha512-NgoaxcNwuCq/jvp77dmGzyS7JxzX4dvD/lAYi/GUhyxEC3TK3teZ3ADRhrC6tb84OpaelPLaTkhRNSaxVAQzjQ==
  dependencies:
    "@nextui-org/aria-utils" "2.2.7"
    "@nextui-org/dom-animation" "2.1.1"
    "@nextui-org/framer-utils" "2.1.6"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@nextui-org/use-safe-layout-effect" "2.1.1"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/overlays" "3.24.0"
    "@react-aria/tooltip" "3.7.10"
    "@react-aria/utils" "3.26.0"
    "@react-stately/tooltip" "3.5.0"
    "@react-types/overlays" "3.8.11"
    "@react-types/tooltip" "3.4.13"

"@nextui-org/use-aria-accordion@2.2.2":
  version "2.2.2"
  resolved "https://registry.npmjs.org/@nextui-org/use-aria-accordion/-/use-aria-accordion-2.2.2.tgz"
  integrity sha512-M8gjX6XmB83cIAZKV2zI1KvmTuuOh+Si50F3SWvYjBXyrDIM5775xCs2PG6AcLjf6OONTl5KwuZ2cbSDHiui6A==
  dependencies:
    "@react-aria/button" "3.11.0"
    "@react-aria/focus" "3.19.0"
    "@react-aria/selection" "3.21.0"
    "@react-aria/utils" "3.26.0"
    "@react-stately/tree" "3.8.6"
    "@react-types/accordion" "3.0.0-alpha.25"
    "@react-types/shared" "3.26.0"

"@nextui-org/use-aria-button@2.2.4":
  version "2.2.4"
  resolved "https://registry.npmjs.org/@nextui-org/use-aria-button/-/use-aria-button-2.2.4.tgz"
  integrity sha512-Bz8l4JGzRKh6V58VX8Laq4rKZDppsnVuNCBHpMJuLo2F9ht7UKvZAEJwXcdbUZ87aui/ZC+IPYqgjvT+d8QlQg==
  dependencies:
    "@nextui-org/shared-utils" "2.1.2"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/utils" "3.26.0"
    "@react-types/button" "3.10.1"
    "@react-types/shared" "3.26.0"

"@nextui-org/use-aria-link@2.2.5":
  version "2.2.5"
  resolved "https://registry.npmjs.org/@nextui-org/use-aria-link/-/use-aria-link-2.2.5.tgz"
  integrity sha512-LBWXLecvuET4ZcpoHyyuS3yxvCzXdkmFcODhYwUmC8PiFSEUHkuFMC+fLwdXCP5GOqrv6wTGYHf41wNy1ugX1w==
  dependencies:
    "@nextui-org/shared-utils" "2.1.2"
    "@react-aria/focus" "3.19.0"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/utils" "3.26.0"
    "@react-types/link" "3.5.9"
    "@react-types/shared" "3.26.0"

"@nextui-org/use-aria-modal-overlay@2.2.3":
  version "2.2.3"
  resolved "https://registry.npmjs.org/@nextui-org/use-aria-modal-overlay/-/use-aria-modal-overlay-2.2.3.tgz"
  integrity sha512-55DIVY0u+Ynxy1/DtzZkMsdVW63wC0mafKXACwCi0xV64D0Ggi9MM7BRePLK0mOboSb3gjCwYqn12gmRiy+kmg==
  dependencies:
    "@react-aria/overlays" "3.24.0"
    "@react-aria/utils" "3.26.0"
    "@react-stately/overlays" "3.6.12"
    "@react-types/shared" "3.26.0"

"@nextui-org/use-aria-multiselect@2.4.3":
  version "2.4.3"
  resolved "https://registry.npmjs.org/@nextui-org/use-aria-multiselect/-/use-aria-multiselect-2.4.3.tgz"
  integrity sha512-PwDA4Y5DOx0SMxc277JeZi8tMtaINTwthPhk8SaDrtOBhP+r9owS3T/W9t37xKnmrTerHwaEq4ADGQtm5/VMXQ==
  dependencies:
    "@react-aria/i18n" "3.12.4"
    "@react-aria/interactions" "3.22.5"
    "@react-aria/label" "3.7.13"
    "@react-aria/listbox" "3.13.6"
    "@react-aria/menu" "3.16.0"
    "@react-aria/selection" "3.21.0"
    "@react-aria/utils" "3.26.0"
    "@react-stately/form" "3.1.0"
    "@react-stately/list" "3.11.1"
    "@react-stately/menu" "3.9.0"
    "@react-types/button" "3.10.1"
    "@react-types/overlays" "3.8.11"
    "@react-types/select" "3.9.8"
    "@react-types/shared" "3.26.0"

"@nextui-org/use-callback-ref@2.1.1":
  version "2.1.1"
  resolved "https://registry.npmjs.org/@nextui-org/use-callback-ref/-/use-callback-ref-2.1.1.tgz"
  integrity sha512-DzlKJ9p7Tm0x3HGjynZ/CgS1jfoBILXKFXnYPLr/SSETXqVaCguixolT/07BRB1yo9AGwELaCEt91BeI0Rb6hQ==
  dependencies:
    "@nextui-org/use-safe-layout-effect" "2.1.1"

"@nextui-org/use-clipboard@2.1.2":
  version "2.1.2"
  resolved "https://registry.npmjs.org/@nextui-org/use-clipboard/-/use-clipboard-2.1.2.tgz"
  integrity sha512-MUITEPaQAvu9VuMCUQXMc4j3uBgXoD8LVcuuvUVucg/8HK/Xia0dQ4QgK30QlCbZ/BwZ047rgMAgpMZeVKw4MQ==

"@nextui-org/use-data-scroll-overflow@2.2.2":
  version "2.2.2"
  resolved "https://registry.npmjs.org/@nextui-org/use-data-scroll-overflow/-/use-data-scroll-overflow-2.2.2.tgz"
  integrity sha512-TFB6BuaLOsE++K1UEIPR9StkBgj9Cvvc+ccETYpmn62B7pK44DmxjkwhK0ei59wafJPIyytZ3DgdVDblfSyIXA==
  dependencies:
    "@nextui-org/shared-utils" "2.1.2"

"@nextui-org/use-disclosure@2.2.2":
  version "2.2.2"
  resolved "https://registry.npmjs.org/@nextui-org/use-disclosure/-/use-disclosure-2.2.2.tgz"
  integrity sha512-ka+5Fic2MIYtOMHi3zomtkWxCWydmJmcq7+fb6RHspfr0tGYjXWYO/lgtGeHFR1LYksMPLID3c7shT5bqzxJcA==
  dependencies:
    "@nextui-org/use-callback-ref" "2.1.1"
    "@react-aria/utils" "3.26.0"
    "@react-stately/utils" "3.10.5"

"@nextui-org/use-draggable@2.1.2":
  version "2.1.2"
  resolved "https://registry.npmjs.org/@nextui-org/use-draggable/-/use-draggable-2.1.2.tgz"
  integrity sha512-gN4G42uuRyFlAZ3FgMSeZLBg3LIeGlKTOLRe3JvyaBn1D1mA2+I3XONY1oKd9KKmtYCJNwY/2x6MVsBfy8nsgw==
  dependencies:
    "@react-aria/interactions" "3.22.5"

"@nextui-org/use-image@2.1.2":
  version "2.1.2"
  resolved "https://registry.npmjs.org/@nextui-org/use-image/-/use-image-2.1.2.tgz"
  integrity sha512-I46M5gCJK4rZ0qYHPx3kVSF2M2uGaWPwzb3w4Cmx8K9QS+LbUQtRMbD8KOGTHZGA3kBDPvFbAi53Ert4eACrZQ==
  dependencies:
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/use-safe-layout-effect" "2.1.1"

"@nextui-org/use-intersection-observer@2.2.2":
  version "2.2.2"
  resolved "https://registry.npmjs.org/@nextui-org/use-intersection-observer/-/use-intersection-observer-2.2.2.tgz"
  integrity sha512-fS/4m8jnXO7GYpnp/Lp+7bfBEAXPzqsXgqGK6qrp7sfFEAbLzuJp0fONkbIB3F6F3FJrbFOlY+Y5qrHptO7U/Q==
  dependencies:
    "@react-aria/interactions" "3.22.5"
    "@react-aria/ssr" "3.9.7"
    "@react-aria/utils" "3.26.0"
    "@react-types/shared" "3.26.0"

"@nextui-org/use-is-mobile@2.2.2":
  version "2.2.2"
  resolved "https://registry.npmjs.org/@nextui-org/use-is-mobile/-/use-is-mobile-2.2.2.tgz"
  integrity sha512-gcmUL17fhgGdu8JfXF12FZCGATJIATxV4jSql+FNhR+gc+QRRWBRmCJSpMIE2RvGXL777tDvvoh/tjFMB3pW4w==
  dependencies:
    "@react-aria/ssr" "3.9.7"

"@nextui-org/use-is-mounted@2.1.1":
  version "2.1.1"
  resolved "https://registry.npmjs.org/@nextui-org/use-is-mounted/-/use-is-mounted-2.1.1.tgz"
  integrity sha512-osJB3E/DCu4Le0f+pb21ia9/TaSHwme4r0fHjO5/nUBYk/RCvGlRUUCJClf/wi9WfH8QyjuJ27+zBcUSm6AMMg==

"@nextui-org/use-measure@2.1.1":
  version "2.1.1"
  resolved "https://registry.npmjs.org/@nextui-org/use-measure/-/use-measure-2.1.1.tgz"
  integrity sha512-2RVn90gXHTgt6fvzBH4fzgv3hMDz+SEJkqaCTbd6WUNWag4AaLb2WU/65CtLcexyu10HrgYf2xG07ZqtJv0zSg==

"@nextui-org/use-pagination@2.2.3":
  version "2.2.3"
  resolved "https://registry.npmjs.org/@nextui-org/use-pagination/-/use-pagination-2.2.3.tgz"
  integrity sha512-V2WGIq4LLkTpq6EUhJg3MVvHY2ZJ63AYV9N0d52Dc3Qqok0tTRuY51dd1P+F58HyTPW84W2z4q2R8XALtzFxQw==
  dependencies:
    "@nextui-org/shared-utils" "2.1.2"
    "@react-aria/i18n" "3.12.4"

"@nextui-org/use-safe-layout-effect@2.1.1":
  version "2.1.1"
  resolved "https://registry.npmjs.org/@nextui-org/use-safe-layout-effect/-/use-safe-layout-effect-2.1.1.tgz"
  integrity sha512-p0vezi2eujC3rxlMQmCLQlc8CNbp+GQgk6YcSm7Rk10isWVlUII5T1L3y+rcFYdgTPObCkCngPPciNQhD7Lf7g==

"@nextui-org/use-scroll-position@2.1.1":
  version "2.1.1"
  resolved "https://registry.npmjs.org/@nextui-org/use-scroll-position/-/use-scroll-position-2.1.1.tgz"
  integrity sha512-RgY1l2POZbSjnEirW51gdb8yNPuQXHqJx3TS8Ut5dk+bhaX9JD3sUdEiJNb3qoHAJInzyjN+27hxnACSlW0gzg==

"@nextui-org/use-update-effect@2.1.1":
  version "2.1.1"
  resolved "https://registry.npmjs.org/@nextui-org/use-update-effect/-/use-update-effect-2.1.1.tgz"
  integrity sha512-fKODihHLWcvDk1Sm8xDua9zjdbstxTOw9shB7k/mPkeR3E7SouSpN0+LW67Bczh1EmbRg1pIrFpEOLnbpgMFzA==

"@nextui-org/user@2.2.6":
  version "2.2.6"
  resolved "https://registry.npmjs.org/@nextui-org/user/-/user-2.2.6.tgz"
  integrity sha512-iimFoP3DVK85p78r0ekC7xpVPQiBIbWnyBPdrnBj1UEgQdKoUzGhVbhYUnA8niBz/AS5xLt6aQixsv9/B0/msw==
  dependencies:
    "@nextui-org/avatar" "2.2.6"
    "@nextui-org/react-utils" "2.1.3"
    "@nextui-org/shared-utils" "2.1.2"
    "@react-aria/focus" "3.19.0"
    "@react-aria/utils" "3.26.0"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  resolved "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@nolyfill/is-core-module@1.0.39":
  version "1.0.39"
  resolved "https://registry.npmjs.org/@nolyfill/is-core-module/-/is-core-module-1.0.39.tgz"
  integrity sha512-nn5ozdjYQpUCZlWGuxcJY/KpxkWQs4DcbMCmKojjyrYDEAGy4Ce19NN4v5MduafTwJlbKc99UA8YhSVqq9yPZA==

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz"
  integrity sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==

"@popperjs/core@^2.11.8":
  version "2.11.8"
  resolved "https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz"
  integrity sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==

"@prisma/client@^5.6.0":
  version "5.22.0"
  resolved "https://registry.npmjs.org/@prisma/client/-/client-5.22.0.tgz"
  integrity sha512-M0SVXfyHnQREBKxCgyo7sffrKttwE6R8PMq330MIUF0pTwjUhLbW84pFDlf06B27XyCR++VtjugEnIHdr07SVA==

"@prisma/debug@5.22.0":
  version "5.22.0"
  resolved "https://registry.npmjs.org/@prisma/debug/-/debug-5.22.0.tgz"
  integrity sha512-AUt44v3YJeggO2ZU5BkXI7M4hu9BF2zzH2iF2V5pyXT/lRTyWiElZ7It+bRH1EshoMRxHgpYg4VB6rCM+mG5jQ==

"@prisma/engines-version@5.22.0-44.605197351a3c8bdd595af2d2a9bc3025bca48ea2":
  version "5.22.0-44.605197351a3c8bdd595af2d2a9bc3025bca48ea2"
  resolved "https://registry.npmjs.org/@prisma/engines-version/-/engines-version-5.22.0-44.605197351a3c8bdd595af2d2a9bc3025bca48ea2.tgz"
  integrity sha512-2PTmxFR2yHW/eB3uqWtcgRcgAbG1rwG9ZriSvQw+nnb7c4uCr3RAcGMb6/zfE88SKlC1Nj2ziUvc96Z379mHgQ==

"@prisma/engines@5.22.0":
  version "5.22.0"
  resolved "https://registry.npmjs.org/@prisma/engines/-/engines-5.22.0.tgz"
  integrity sha512-UNjfslWhAt06kVL3CjkuYpHAWSO6L4kDCVPegV6itt7nD1kSJavd3vhgAEhjglLJJKEdJ7oIqDJ+yHk6qO8gPA==
  dependencies:
    "@prisma/debug" "5.22.0"
    "@prisma/engines-version" "5.22.0-44.605197351a3c8bdd595af2d2a9bc3025bca48ea2"
    "@prisma/fetch-engine" "5.22.0"
    "@prisma/get-platform" "5.22.0"

"@prisma/fetch-engine@5.22.0":
  version "5.22.0"
  resolved "https://registry.npmjs.org/@prisma/fetch-engine/-/fetch-engine-5.22.0.tgz"
  integrity sha512-bkrD/Mc2fSvkQBV5EpoFcZ87AvOgDxbG99488a5cexp5Ccny+UM6MAe/UFkUC0wLYD9+9befNOqGiIJhhq+HbA==
  dependencies:
    "@prisma/debug" "5.22.0"
    "@prisma/engines-version" "5.22.0-44.605197351a3c8bdd595af2d2a9bc3025bca48ea2"
    "@prisma/get-platform" "5.22.0"

"@prisma/get-platform@5.22.0":
  version "5.22.0"
  resolved "https://registry.npmjs.org/@prisma/get-platform/-/get-platform-5.22.0.tgz"
  integrity sha512-pHhpQdr1UPFpt+zFfnPazhulaZYCUqeIcPpJViYoq9R+D/yw4fjE+CtnsnKzPYm0ddUbeXUzjGVGIRVgPDCk4Q==
  dependencies:
    "@prisma/debug" "5.22.0"

"@radix-ui/number@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/number/-/number-1.1.0.tgz"
  integrity sha512-V3gRzhVNU1ldS5XhAPTom1fOIo4ccrjjJgmE+LI2h/WaFpHmx0MQApT+KZHnx8abG6Avtfcz4WoEciMnpFT3HQ==

"@radix-ui/primitive@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.1.tgz"
  integrity sha512-SJ31y+Q/zAyShtXJc8x83i9TYdbAfHZ++tUZnvjJJqFjzsdUnKsxPL6IEtBlxKkU7yzer//GQtZSV4GbldL3YA==

"@radix-ui/react-alert-dialog@^1.1.1":
  version "1.1.6"
  resolved "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.6.tgz"
  integrity sha512-p4XnPqgej8sZAAReCAKgz1REYZEBLR8hU9Pg27wFnCWIMc8g1ccCs0FjBcy05V15VTu8pAePw/VDYeOm/uZ6yQ==
  dependencies:
    "@radix-ui/primitive" "1.1.1"
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-dialog" "1.1.6"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-slot" "1.1.2"

"@radix-ui/react-arrow@1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.2.tgz"
  integrity sha512-G+KcpzXHq24iH0uGG/pF8LyzpFJYGD4RfLjCIBfGdSLXvjLHST31RUiRVrupIBMvIppMgSzQ6l66iAxl03tdlg==
  dependencies:
    "@radix-ui/react-primitive" "2.0.2"

"@radix-ui/react-avatar@^1.0.4":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.3.tgz"
  integrity sha512-Paen00T4P8L8gd9bNsRMw7Cbaz85oxiv+hzomsRZgFm2byltPFDtfcoqlWJ8GyZlIBWgLssJlzLCnKU0G0302g==
  dependencies:
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-use-callback-ref" "1.1.0"
    "@radix-ui/react-use-layout-effect" "1.1.0"

"@radix-ui/react-collection@1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.2.tgz"
  integrity sha512-9z54IEKRxIa9VityapoEYMuByaG42iSy1ZXlY2KcuLSEtq8x4987/N6m15ppoMffgZX72gER2uHe1D9Y6Unlcw==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-slot" "1.1.2"

"@radix-ui/react-compose-refs@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.1.tgz"
  integrity sha512-Y9VzoRDSJtgFMUCoiZBDVo084VQ5hfpXxVE+NgkdNsjiDBByiImMZKKhxMwCbdHvhlENG6a833CbFkOQvTricw==

"@radix-ui/react-context-menu@^2.1.5":
  version "2.2.6"
  resolved "https://registry.npmjs.org/@radix-ui/react-context-menu/-/react-context-menu-2.2.6.tgz"
  integrity sha512-aUP99QZ3VU84NPsHeaFt4cQUNgJqFsLLOt/RbbWXszZ6MP0DpDyjkFZORr4RpAEx3sUBk+Kc8h13yGtC5Qw8dg==
  dependencies:
    "@radix-ui/primitive" "1.1.1"
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-menu" "2.1.6"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-use-callback-ref" "1.1.0"
    "@radix-ui/react-use-controllable-state" "1.1.0"

"@radix-ui/react-context@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1.tgz"
  integrity sha512-UASk9zi+crv9WteK/NU4PLvOoL3OuE6BWVKNF6hPRBtYBDXQ2u5iu3O59zUlJiTVvkyuycnqrztsHVJwcK9K+Q==

"@radix-ui/react-dialog@1.1.6":
  version "1.1.6"
  resolved "https://registry.npmjs.org/@radix-ui/react-dialog/-/react-dialog-1.1.6.tgz"
  integrity sha512-/IVhJV5AceX620DUJ4uYVMymzsipdKBzo3edo+omeskCKGm9FRHM0ebIdbPnlQVJqyuHbuBltQUOG2mOTq2IYw==
  dependencies:
    "@radix-ui/primitive" "1.1.1"
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.5"
    "@radix-ui/react-focus-guards" "1.1.1"
    "@radix-ui/react-focus-scope" "1.1.2"
    "@radix-ui/react-id" "1.1.0"
    "@radix-ui/react-portal" "1.1.4"
    "@radix-ui/react-presence" "1.1.2"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-slot" "1.1.2"
    "@radix-ui/react-use-controllable-state" "1.1.0"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-direction@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-direction/-/react-direction-1.1.0.tgz"
  integrity sha512-BUuBvgThEiAXh2DWu93XsT+a3aWrGqolGlqqw5VU1kG7p/ZH2cuDlM1sRLNnY3QcBS69UIz2mcKhMxDsdewhjg==

"@radix-ui/react-dismissable-layer@1.1.5":
  version "1.1.5"
  resolved "https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.5.tgz"
  integrity sha512-E4TywXY6UsXNRhFrECa5HAvE5/4BFcGyfTyK36gP+pAW1ed7UTK4vKwdr53gAJYwqbfCWC6ATvJa3J3R/9+Qrg==
  dependencies:
    "@radix-ui/primitive" "1.1.1"
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-use-callback-ref" "1.1.0"
    "@radix-ui/react-use-escape-keydown" "1.1.0"

"@radix-ui/react-focus-guards@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.1.tgz"
  integrity sha512-pSIwfrT1a6sIoDASCSpFwOasEwKTZWDw/iBdtnqKO7v6FeOzYJ7U53cPzYFVR3geGGXgVHaH+CdngrrAzqUGxg==

"@radix-ui/react-focus-scope@1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.2.tgz"
  integrity sha512-zxwE80FCU7lcXUGWkdt6XpTTCKPitG1XKOwViTxHVKIJhZl9MvIl2dVHeZENCWD9+EdWv05wlaEkRXUykU27RA==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-use-callback-ref" "1.1.0"

"@radix-ui/react-icons@^1.3.0":
  version "1.3.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-icons/-/react-icons-1.3.2.tgz"
  integrity sha512-fyQIhGDhzfc9pK2kH6Pl9c4BDJGfMkPqkyIgYDthyNYoNg3wVhoJMMh19WS4Up/1KMPFVpNsT2q3WmXn2N1m6g==

"@radix-ui/react-id@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-id/-/react-id-1.1.0.tgz"
  integrity sha512-EJUrI8yYh7WOjNOqpoJaf1jlFIH2LvtgAl+YcFqNCa+4hj64ZXmPkAKOFs/ukjz3byN6bdb/AVUqHkI8/uWWMA==
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.0"

"@radix-ui/react-label@^2.0.2":
  version "2.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.2.tgz"
  integrity sha512-zo1uGMTaNlHehDyFQcDZXRJhUPDuukcnHz0/jnrup0JA6qL+AFpAnty+7VKa9esuU5xTblAZzTGYJKSKaBxBhw==
  dependencies:
    "@radix-ui/react-primitive" "2.0.2"

"@radix-ui/react-menu@2.1.6":
  version "2.1.6"
  resolved "https://registry.npmjs.org/@radix-ui/react-menu/-/react-menu-2.1.6.tgz"
  integrity sha512-tBBb5CXDJW3t2mo9WlO7r6GTmWV0F0uzHZVFmlRmYpiSK1CDU5IKojP1pm7oknpBOrFZx/YgBRW9oorPO2S/Lg==
  dependencies:
    "@radix-ui/primitive" "1.1.1"
    "@radix-ui/react-collection" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-direction" "1.1.0"
    "@radix-ui/react-dismissable-layer" "1.1.5"
    "@radix-ui/react-focus-guards" "1.1.1"
    "@radix-ui/react-focus-scope" "1.1.2"
    "@radix-ui/react-id" "1.1.0"
    "@radix-ui/react-popper" "1.2.2"
    "@radix-ui/react-portal" "1.1.4"
    "@radix-ui/react-presence" "1.1.2"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-roving-focus" "1.1.2"
    "@radix-ui/react-slot" "1.1.2"
    "@radix-ui/react-use-callback-ref" "1.1.0"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-popper@1.2.2":
  version "1.2.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.2.tgz"
  integrity sha512-Rvqc3nOpwseCyj/rgjlJDYAgyfw7OC1tTkKn2ivhaMGcYt8FSBlahHOZak2i3QwkRXUXgGgzeEe2RuqeEHuHgA==
  dependencies:
    "@floating-ui/react-dom" "^2.0.0"
    "@radix-ui/react-arrow" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-use-callback-ref" "1.1.0"
    "@radix-ui/react-use-layout-effect" "1.1.0"
    "@radix-ui/react-use-rect" "1.1.0"
    "@radix-ui/react-use-size" "1.1.0"
    "@radix-ui/rect" "1.1.0"

"@radix-ui/react-portal@1.1.4":
  version "1.1.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.4.tgz"
  integrity sha512-sn2O9k1rPFYVyKd5LAJfo96JlSGVFpa1fS6UuBJfrZadudiw5tAmru+n1x7aMRQ84qDM71Zh1+SzK5QwU0tJfA==
  dependencies:
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-use-layout-effect" "1.1.0"

"@radix-ui/react-presence@1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.2.tgz"
  integrity sha512-18TFr80t5EVgL9x1SwF/YGtfG+l0BS0PRAlCWBDoBEiDQjeKgnNZRVJp/oVBl24sr3Gbfwc/Qpj4OcWTQMsAEg==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.0"

"@radix-ui/react-primitive@2.0.2":
  version "2.0.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.2.tgz"
  integrity sha512-Ec/0d38EIuvDF+GZjcMU/Ze6MxntVJYO/fRlCPhCaVUyPY9WTalHJw54tp9sXeJo3tlShWpy41vQRgLRGOuz+w==
  dependencies:
    "@radix-ui/react-slot" "1.1.2"

"@radix-ui/react-progress@^1.1.0":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-progress/-/react-progress-1.1.2.tgz"
  integrity sha512-u1IgJFQ4zNAUTjGdDL5dcl/U8ntOR6jsnhxKb5RKp5Ozwl88xKR9EqRZOe/Mk8tnx0x5tNUe2F+MzsyjqMg0MA==
  dependencies:
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-primitive" "2.0.2"

"@radix-ui/react-roving-focus@1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-roving-focus/-/react-roving-focus-1.1.2.tgz"
  integrity sha512-zgMQWkNO169GtGqRvYrzb0Zf8NhMHS2DuEB/TiEmVnpr5OqPU3i8lfbxaAmC2J/KYuIQxyoQQ6DxepyXp61/xw==
  dependencies:
    "@radix-ui/primitive" "1.1.1"
    "@radix-ui/react-collection" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-direction" "1.1.0"
    "@radix-ui/react-id" "1.1.0"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-use-callback-ref" "1.1.0"
    "@radix-ui/react-use-controllable-state" "1.1.0"

"@radix-ui/react-scroll-area@^1.0.5":
  version "1.2.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.3.tgz"
  integrity sha512-l7+NNBfBYYJa9tNqVcP2AGvxdE3lmE6kFTBXdvHgUaZuy+4wGCL1Cl2AfaR7RKyimj7lZURGLwFO59k4eBnDJQ==
  dependencies:
    "@radix-ui/number" "1.1.0"
    "@radix-ui/primitive" "1.1.1"
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-direction" "1.1.0"
    "@radix-ui/react-presence" "1.1.2"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-use-callback-ref" "1.1.0"
    "@radix-ui/react-use-layout-effect" "1.1.0"

"@radix-ui/react-select@^2.1.1":
  version "2.1.6"
  resolved "https://registry.npmjs.org/@radix-ui/react-select/-/react-select-2.1.6.tgz"
  integrity sha512-T6ajELxRvTuAMWH0YmRJ1qez+x4/7Nq7QIx7zJ0VK3qaEWdnWpNbEDnmWldG1zBDwqrLy5aLMUWcoGirVj5kMg==
  dependencies:
    "@radix-ui/number" "1.1.0"
    "@radix-ui/primitive" "1.1.1"
    "@radix-ui/react-collection" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-direction" "1.1.0"
    "@radix-ui/react-dismissable-layer" "1.1.5"
    "@radix-ui/react-focus-guards" "1.1.1"
    "@radix-ui/react-focus-scope" "1.1.2"
    "@radix-ui/react-id" "1.1.0"
    "@radix-ui/react-popper" "1.2.2"
    "@radix-ui/react-portal" "1.1.4"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-slot" "1.1.2"
    "@radix-ui/react-use-callback-ref" "1.1.0"
    "@radix-ui/react-use-controllable-state" "1.1.0"
    "@radix-ui/react-use-layout-effect" "1.1.0"
    "@radix-ui/react-use-previous" "1.1.0"
    "@radix-ui/react-visually-hidden" "1.1.2"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-separator@^1.0.3":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-separator/-/react-separator-1.1.2.tgz"
  integrity sha512-oZfHcaAp2Y6KFBX6I5P1u7CQoy4lheCGiYj+pGFrHy8E/VNRb5E39TkTr3JrV520csPBTZjkuKFdEsjS5EUNKQ==
  dependencies:
    "@radix-ui/react-primitive" "2.0.2"

"@radix-ui/react-slider@^1.1.2":
  version "1.2.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.3.tgz"
  integrity sha512-nNrLAWLjGESnhqBqcCNW4w2nn7LxudyMzeB6VgdyAnFLC6kfQgnAjSL2v6UkQTnDctJBlxrmxfplWS4iYjdUTw==
  dependencies:
    "@radix-ui/number" "1.1.0"
    "@radix-ui/primitive" "1.1.1"
    "@radix-ui/react-collection" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-direction" "1.1.0"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-use-controllable-state" "1.1.0"
    "@radix-ui/react-use-layout-effect" "1.1.0"
    "@radix-ui/react-use-previous" "1.1.0"
    "@radix-ui/react-use-size" "1.1.0"

"@radix-ui/react-slot@^1.0.2", "@radix-ui/react-slot@1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.1.2.tgz"
  integrity sha512-YAKxaiGsSQJ38VzKH86/BPRC4rh+b1Jpa+JneA5LRE7skmLPNAyeG8kPJj/oo4STLvlrs8vkf/iYyc3A5stYCQ==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.1"

"@radix-ui/react-switch@^1.0.3":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.3.tgz"
  integrity sha512-1nc+vjEOQkJVsJtWPSiISGT6OKm4SiOdjMo+/icLxo2G4vxz1GntC5MzfL4v8ey9OEfw787QCD1y3mUv0NiFEQ==
  dependencies:
    "@radix-ui/primitive" "1.1.1"
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-use-controllable-state" "1.1.0"
    "@radix-ui/react-use-previous" "1.1.0"
    "@radix-ui/react-use-size" "1.1.0"

"@radix-ui/react-tabs@^1.0.4":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.3.tgz"
  integrity sha512-9mFyI30cuRDImbmFF6O2KUJdgEOsGh9Vmx9x/Dh9tOhL7BngmQPQfwW4aejKm5OHpfWIdmeV6ySyuxoOGjtNng==
  dependencies:
    "@radix-ui/primitive" "1.1.1"
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-direction" "1.1.0"
    "@radix-ui/react-id" "1.1.0"
    "@radix-ui/react-presence" "1.1.2"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-roving-focus" "1.1.2"
    "@radix-ui/react-use-controllable-state" "1.1.0"

"@radix-ui/react-toast@^1.1.5":
  version "1.2.6"
  resolved "https://registry.npmjs.org/@radix-ui/react-toast/-/react-toast-1.2.6.tgz"
  integrity sha512-gN4dpuIVKEgpLn1z5FhzT9mYRUitbfZq9XqN/7kkBMUgFTzTG8x/KszWJugJXHcwxckY8xcKDZPz7kG3o6DsUA==
  dependencies:
    "@radix-ui/primitive" "1.1.1"
    "@radix-ui/react-collection" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.5"
    "@radix-ui/react-portal" "1.1.4"
    "@radix-ui/react-presence" "1.1.2"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-use-callback-ref" "1.1.0"
    "@radix-ui/react-use-controllable-state" "1.1.0"
    "@radix-ui/react-use-layout-effect" "1.1.0"
    "@radix-ui/react-visually-hidden" "1.1.2"

"@radix-ui/react-toggle@^1.0.3":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.2.tgz"
  integrity sha512-lntKchNWx3aCHuWKiDY+8WudiegQvBpDRAYL8dKLRvKEH8VOpl0XX6SSU/bUBqIRJbcTy4+MW06Wv8vgp10rzQ==
  dependencies:
    "@radix-ui/primitive" "1.1.1"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-use-controllable-state" "1.1.0"

"@radix-ui/react-tooltip@^1.1.2":
  version "1.1.8"
  resolved "https://registry.npmjs.org/@radix-ui/react-tooltip/-/react-tooltip-1.1.8.tgz"
  integrity sha512-YAA2cu48EkJZdAMHC0dqo9kialOcRStbtiY4nJPaht7Ptrhcvpo+eDChaM6BIs8kL6a8Z5l5poiqLnXcNduOkA==
  dependencies:
    "@radix-ui/primitive" "1.1.1"
    "@radix-ui/react-compose-refs" "1.1.1"
    "@radix-ui/react-context" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.5"
    "@radix-ui/react-id" "1.1.0"
    "@radix-ui/react-popper" "1.2.2"
    "@radix-ui/react-portal" "1.1.4"
    "@radix-ui/react-presence" "1.1.2"
    "@radix-ui/react-primitive" "2.0.2"
    "@radix-ui/react-slot" "1.1.2"
    "@radix-ui/react-use-controllable-state" "1.1.0"
    "@radix-ui/react-visually-hidden" "1.1.2"

"@radix-ui/react-use-callback-ref@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.0.tgz"
  integrity sha512-CasTfvsy+frcFkbXtSJ2Zu9JHpN8TYKxkgJGWbjiZhFivxaeW7rMeZt7QELGVLaYVfFMsKHjb7Ak0nMEe+2Vfw==

"@radix-ui/react-use-controllable-state@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.1.0.tgz"
  integrity sha512-MtfMVJiSr2NjzS0Aa90NPTnvTSg6C/JLCV7ma0W6+OMV78vd8OyRpID+Ng9LxzsPbLeuBnWBA1Nq30AtBIDChw==
  dependencies:
    "@radix-ui/react-use-callback-ref" "1.1.0"

"@radix-ui/react-use-escape-keydown@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.0.tgz"
  integrity sha512-L7vwWlR1kTTQ3oh7g1O0CBF3YCyyTj8NmhLR+phShpyA50HCfBFKVJTpshm9PzLiKmehsrQzTYTpX9HvmC9rhw==
  dependencies:
    "@radix-ui/react-use-callback-ref" "1.1.0"

"@radix-ui/react-use-layout-effect@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.0.tgz"
  integrity sha512-+FPE0rOdziWSrH9athwI1R0HDVbWlEhd+FR+aSDk4uWGmSJ9Z54sdZVDQPZAinJhJXwfT+qnj969mCsT2gfm5w==

"@radix-ui/react-use-previous@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.0.tgz"
  integrity sha512-Z/e78qg2YFnnXcW88A4JmTtm4ADckLno6F7OXotmkQfeuCVaKuYzqAATPhVzl3delXE7CxIV8shofPn3jPc5Og==

"@radix-ui/react-use-rect@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.0.tgz"
  integrity sha512-0Fmkebhr6PiseyZlYAOtLS+nb7jLmpqTrJyv61Pe68MKYW6OWdRE2kI70TaYY27u7H0lajqM3hSMMLFq18Z7nQ==
  dependencies:
    "@radix-ui/rect" "1.1.0"

"@radix-ui/react-use-size@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-size/-/react-use-size-1.1.0.tgz"
  integrity sha512-XW3/vWuIXHa+2Uwcc2ABSfcCledmXhhQPlGbfcRXbiUQI5Icjcg19BGCZVKKInYbvUCut/ufbbLLPFC5cbb1hw==
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.0"

"@radix-ui/react-visually-hidden@1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-visually-hidden/-/react-visually-hidden-1.1.2.tgz"
  integrity sha512-1SzA4ns2M1aRlvxErqhLHsBHoS5eI5UUcI2awAMgGUp4LoaoWOKYmvqDY2s/tltuPkh3Yk77YF/r3IRj+Amx4Q==
  dependencies:
    "@radix-ui/react-primitive" "2.0.2"

"@radix-ui/rect@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@radix-ui/rect/-/rect-1.1.0.tgz"
  integrity sha512-A9+lCBZoaMJlVKcRBz2YByCG+Cp2t6nAnMnNba+XiWxnj6r4JUFqfsgwocMBZU9LPtdxC6wB56ySYpc7LQIoJg==

"@react-aria/breadcrumbs@3.5.19":
  version "3.5.19"
  resolved "https://registry.npmjs.org/@react-aria/breadcrumbs/-/breadcrumbs-3.5.19.tgz"
  integrity sha512-mVngOPFYVVhec89rf/CiYQGTfaLRfHFtX+JQwY7sNYNqSA+gO8p4lNARe3Be6bJPgH+LUQuruIY9/ZDL6LT3HA==
  dependencies:
    "@react-aria/i18n" "^3.12.4"
    "@react-aria/link" "^3.7.7"
    "@react-aria/utils" "^3.26.0"
    "@react-types/breadcrumbs" "^3.7.9"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/button@3.11.0":
  version "3.11.0"
  resolved "https://registry.npmjs.org/@react-aria/button/-/button-3.11.0.tgz"
  integrity sha512-b37eIV6IW11KmNIAm65F3SEl2/mgj5BrHIysW6smZX3KoKWTGYsYfcQkmtNgY0GOSFfDxMCoolsZ6mxC00nSDA==
  dependencies:
    "@react-aria/focus" "^3.19.0"
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/toolbar" "3.0.0-beta.11"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/toggle" "^3.8.0"
    "@react-types/button" "^3.10.1"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/calendar@3.6.0":
  version "3.6.0"
  resolved "https://registry.npmjs.org/@react-aria/calendar/-/calendar-3.6.0.tgz"
  integrity sha512-tZ3nd5DP8uxckbj83Pt+4RqgcTWDlGi7njzc7QqFOG2ApfnYDUXbIpb/Q4KY6JNlJskG8q33wo0XfOwNy8J+eg==
  dependencies:
    "@internationalized/date" "^3.6.0"
    "@react-aria/i18n" "^3.12.4"
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/live-announcer" "^3.4.1"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/calendar" "^3.6.0"
    "@react-types/button" "^3.10.1"
    "@react-types/calendar" "^3.5.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/checkbox@3.15.0":
  version "3.15.0"
  resolved "https://registry.npmjs.org/@react-aria/checkbox/-/checkbox-3.15.0.tgz"
  integrity sha512-z/8xd4em7o0MroBXwkkwv7QRwiJaA1FwqMhRUb7iqtBGP2oSytBEDf0N7L09oci32a1P4ZPz2rMK5GlLh/PD6g==
  dependencies:
    "@react-aria/form" "^3.0.11"
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/label" "^3.7.13"
    "@react-aria/toggle" "^3.10.10"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/checkbox" "^3.6.10"
    "@react-stately/form" "^3.1.0"
    "@react-stately/toggle" "^3.8.0"
    "@react-types/checkbox" "^3.9.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/combobox@3.11.0":
  version "3.11.0"
  resolved "https://registry.npmjs.org/@react-aria/combobox/-/combobox-3.11.0.tgz"
  integrity sha512-s88YMmPkMO1WSoiH1KIyZDLJqUwvM2wHXXakj3cYw1tBHGo4rOUFq+JWQIbM5EDO4HOR4AUUqzIUd0NO7t3zyg==
  dependencies:
    "@react-aria/i18n" "^3.12.4"
    "@react-aria/listbox" "^3.13.6"
    "@react-aria/live-announcer" "^3.4.1"
    "@react-aria/menu" "^3.16.0"
    "@react-aria/overlays" "^3.24.0"
    "@react-aria/selection" "^3.21.0"
    "@react-aria/textfield" "^3.15.0"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/collections" "^3.12.0"
    "@react-stately/combobox" "^3.10.1"
    "@react-stately/form" "^3.1.0"
    "@react-types/button" "^3.10.1"
    "@react-types/combobox" "^3.13.1"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/datepicker@3.12.0":
  version "3.12.0"
  resolved "https://registry.npmjs.org/@react-aria/datepicker/-/datepicker-3.12.0.tgz"
  integrity sha512-VYNXioLfddIHpwQx211+rTYuunDmI7VHWBRetCpH3loIsVFuhFSRchTQpclAzxolO3g0vO7pMVj9VYt7Swp6kg==
  dependencies:
    "@internationalized/date" "^3.6.0"
    "@internationalized/number" "^3.6.0"
    "@internationalized/string" "^3.2.5"
    "@react-aria/focus" "^3.19.0"
    "@react-aria/form" "^3.0.11"
    "@react-aria/i18n" "^3.12.4"
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/label" "^3.7.13"
    "@react-aria/spinbutton" "^3.6.10"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/datepicker" "^3.11.0"
    "@react-stately/form" "^3.1.0"
    "@react-types/button" "^3.10.1"
    "@react-types/calendar" "^3.5.0"
    "@react-types/datepicker" "^3.9.0"
    "@react-types/dialog" "^3.5.14"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/dialog@3.5.20":
  version "3.5.20"
  resolved "https://registry.npmjs.org/@react-aria/dialog/-/dialog-3.5.20.tgz"
  integrity sha512-l0GZVLgeOd3kL3Yj8xQW7wN3gn9WW3RLd/SGI9t7ciTq+I/FhftjXCWzXLlOCCTLMf+gv7eazecECtmoWUaZWQ==
  dependencies:
    "@react-aria/focus" "^3.19.0"
    "@react-aria/overlays" "^3.24.0"
    "@react-aria/utils" "^3.26.0"
    "@react-types/dialog" "^3.5.14"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/focus@^3.19.0", "@react-aria/focus@3.19.0":
  version "3.19.0"
  resolved "https://registry.npmjs.org/@react-aria/focus/-/focus-3.19.0.tgz"
  integrity sha512-hPF9EXoUQeQl1Y21/rbV2H4FdUR2v+4/I0/vB+8U3bT1CJ+1AFj1hc/rqx2DqEwDlEwOHN+E4+mRahQmlybq0A==
  dependencies:
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/utils" "^3.26.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"
    clsx "^2.0.0"

"@react-aria/focus@^3.20.0":
  version "3.20.0"
  resolved "https://registry.npmjs.org/@react-aria/focus/-/focus-3.20.0.tgz"
  integrity sha512-KXZCwWzwnmtUo6xhnyV26ptxlxmqd0Reez7axduqqqeDDgDZOVscoo/5gFg71fdPZmnDC8MyUK1vxSbMhOTrGg==
  dependencies:
    "@react-aria/interactions" "^3.24.0"
    "@react-aria/utils" "^3.28.0"
    "@react-types/shared" "^3.28.0"
    "@swc/helpers" "^0.5.0"
    clsx "^2.0.0"

"@react-aria/form@^3.0.11", "@react-aria/form@3.0.11":
  version "3.0.11"
  resolved "https://registry.npmjs.org/@react-aria/form/-/form-3.0.11.tgz"
  integrity sha512-oXzjTiwVuuWjZ8muU0hp3BrDH5qjVctLOF50mjPvqUbvXQTHhoDxWweyIXPQjGshaqBd2w4pWaE4A2rG2O/apw==
  dependencies:
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/form" "^3.1.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/grid@^3.11.0":
  version "3.12.0"
  resolved "https://registry.npmjs.org/@react-aria/grid/-/grid-3.12.0.tgz"
  integrity sha512-w1hVZP73QX/9YCc9pXBJzT71m3mKAHcNI68Z25NbQqwr774b5g1fdcO8o7knnlKXZVsM+Vz30kdRP8iG5GqY6A==
  dependencies:
    "@react-aria/focus" "^3.20.0"
    "@react-aria/i18n" "^3.12.6"
    "@react-aria/interactions" "^3.24.0"
    "@react-aria/live-announcer" "^3.4.1"
    "@react-aria/selection" "^3.23.0"
    "@react-aria/utils" "^3.28.0"
    "@react-stately/collections" "^3.12.2"
    "@react-stately/grid" "^3.11.0"
    "@react-stately/selection" "^3.20.0"
    "@react-types/checkbox" "^3.9.2"
    "@react-types/grid" "^3.3.0"
    "@react-types/shared" "^3.28.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/i18n@^3.12.4", "@react-aria/i18n@3.12.4":
  version "3.12.4"
  resolved "https://registry.npmjs.org/@react-aria/i18n/-/i18n-3.12.4.tgz"
  integrity sha512-j9+UL3q0Ls8MhXV9gtnKlyozq4aM95YywXqnmJtzT1rYeBx7w28hooqrWkCYLfqr4OIryv1KUnPiCSLwC2OC7w==
  dependencies:
    "@internationalized/date" "^3.6.0"
    "@internationalized/message" "^3.1.6"
    "@internationalized/number" "^3.6.0"
    "@internationalized/string" "^3.2.5"
    "@react-aria/ssr" "^3.9.7"
    "@react-aria/utils" "^3.26.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/i18n@^3.12.6":
  version "3.12.6"
  resolved "https://registry.npmjs.org/@react-aria/i18n/-/i18n-3.12.6.tgz"
  integrity sha512-I2Qz1vAlgdeW2GUMLhHucYhk514/BRuEzvH1iih8qeqvv0gEbKdSIjPJUomW+WzYVmJ2/bwKQAr7otr2fNcbrw==
  dependencies:
    "@internationalized/date" "^3.7.0"
    "@internationalized/message" "^3.1.6"
    "@internationalized/number" "^3.6.0"
    "@internationalized/string" "^3.2.5"
    "@react-aria/ssr" "^3.9.7"
    "@react-aria/utils" "^3.28.0"
    "@react-types/shared" "^3.28.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/interactions@^3.22.5", "@react-aria/interactions@3.22.5":
  version "3.22.5"
  resolved "https://registry.npmjs.org/@react-aria/interactions/-/interactions-3.22.5.tgz"
  integrity sha512-kMwiAD9E0TQp+XNnOs13yVJghiy8ET8L0cbkeuTgNI96sOAp/63EJ1FSrDf17iD8sdjt41LafwX/dKXW9nCcLQ==
  dependencies:
    "@react-aria/ssr" "^3.9.7"
    "@react-aria/utils" "^3.26.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/interactions@^3.24.0":
  version "3.24.0"
  resolved "https://registry.npmjs.org/@react-aria/interactions/-/interactions-3.24.0.tgz"
  integrity sha512-6Zdhp1pswyPgbwEWzvXARdKAWPjP7mACczoIUvlEQiMsX04fuizBiBLAA+W/5mPe17pbJYHA/rxZF5Y5m+M0Ng==
  dependencies:
    "@react-aria/ssr" "^3.9.7"
    "@react-aria/utils" "^3.28.0"
    "@react-stately/flags" "^3.1.0"
    "@react-types/shared" "^3.28.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/label@^3.7.13", "@react-aria/label@3.7.13":
  version "3.7.13"
  resolved "https://registry.npmjs.org/@react-aria/label/-/label-3.7.13.tgz"
  integrity sha512-brSAXZVTey5RG/Ex6mTrV/9IhGSQFU4Al34qmjEDho+Z2qT4oPwf8k7TRXWWqzOU0ugYxekYbsLd2zlN3XvWcg==
  dependencies:
    "@react-aria/utils" "^3.26.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/link@^3.7.7", "@react-aria/link@3.7.7":
  version "3.7.7"
  resolved "https://registry.npmjs.org/@react-aria/link/-/link-3.7.7.tgz"
  integrity sha512-eVBRcHKhNSsATYWv5wRnZXRqPVcKAWWakyvfrYePIKpC3s4BaHZyTGYdefk8ZwZdEOuQZBqLMnjW80q1uhtkuA==
  dependencies:
    "@react-aria/focus" "^3.19.0"
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/utils" "^3.26.0"
    "@react-types/link" "^3.5.9"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/listbox@^3.13.6", "@react-aria/listbox@3.13.6":
  version "3.13.6"
  resolved "https://registry.npmjs.org/@react-aria/listbox/-/listbox-3.13.6.tgz"
  integrity sha512-6hEXEXIZVau9lgBZ4VVjFR3JnGU+fJaPmV3HP0UZ2ucUptfG0MZo24cn+ZQJsWiuaCfNFv5b8qribiv+BcO+Kg==
  dependencies:
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/label" "^3.7.13"
    "@react-aria/selection" "^3.21.0"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/collections" "^3.12.0"
    "@react-stately/list" "^3.11.1"
    "@react-types/listbox" "^3.5.3"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/live-announcer@^3.4.1":
  version "3.4.1"
  resolved "https://registry.npmjs.org/@react-aria/live-announcer/-/live-announcer-3.4.1.tgz"
  integrity sha512-4X2mcxgqLvvkqxv2l1n00jTzUxxe0kkLiapBGH1LHX/CxA1oQcHDqv8etJ2ZOwmS/MSBBiWnv3DwYHDOF6ubig==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@react-aria/menu@^3.16.0", "@react-aria/menu@3.16.0":
  version "3.16.0"
  resolved "https://registry.npmjs.org/@react-aria/menu/-/menu-3.16.0.tgz"
  integrity sha512-TNk+Vd3TbpBPUxEloAdHRTaRxf9JBK7YmkHYiq0Yj5Lc22KS0E2eTyhpPM9xJvEWN2TlC5TEvNfdyui2kYWFFQ==
  dependencies:
    "@react-aria/focus" "^3.19.0"
    "@react-aria/i18n" "^3.12.4"
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/overlays" "^3.24.0"
    "@react-aria/selection" "^3.21.0"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/collections" "^3.12.0"
    "@react-stately/menu" "^3.9.0"
    "@react-stately/selection" "^3.18.0"
    "@react-stately/tree" "^3.8.6"
    "@react-types/button" "^3.10.1"
    "@react-types/menu" "^3.9.13"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/overlays@^3.24.0", "@react-aria/overlays@3.24.0":
  version "3.24.0"
  resolved "https://registry.npmjs.org/@react-aria/overlays/-/overlays-3.24.0.tgz"
  integrity sha512-0kAXBsMNTc/a3M07tK9Cdt/ea8CxTAEJ223g8YgqImlmoBBYAL7dl5G01IOj67TM64uWPTmZrOklBchHWgEm3A==
  dependencies:
    "@react-aria/focus" "^3.19.0"
    "@react-aria/i18n" "^3.12.4"
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/ssr" "^3.9.7"
    "@react-aria/utils" "^3.26.0"
    "@react-aria/visually-hidden" "^3.8.18"
    "@react-stately/overlays" "^3.6.12"
    "@react-types/button" "^3.10.1"
    "@react-types/overlays" "^3.8.11"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/progress@3.4.18":
  version "3.4.18"
  resolved "https://registry.npmjs.org/@react-aria/progress/-/progress-3.4.18.tgz"
  integrity sha512-FOLgJ9t9i1u3oAAimybJG6r7/soNPBnJfWo4Yr6MmaUv90qVGa1h6kiuM5m9H/bm5JobAebhdfHit9lFlgsCmg==
  dependencies:
    "@react-aria/i18n" "^3.12.4"
    "@react-aria/label" "^3.7.13"
    "@react-aria/utils" "^3.26.0"
    "@react-types/progress" "^3.5.8"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/radio@3.10.10":
  version "3.10.10"
  resolved "https://registry.npmjs.org/@react-aria/radio/-/radio-3.10.10.tgz"
  integrity sha512-NVdeOVrsrHgSfwL2jWCCXFsWZb+RMRZErj5vthHQW4nkHECGOzeX56VaLWTSvdoCPqi9wdIX8A6K9peeAIgxzA==
  dependencies:
    "@react-aria/focus" "^3.19.0"
    "@react-aria/form" "^3.0.11"
    "@react-aria/i18n" "^3.12.4"
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/label" "^3.7.13"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/radio" "^3.10.9"
    "@react-types/radio" "^3.8.5"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/selection@^3.21.0", "@react-aria/selection@3.21.0":
  version "3.21.0"
  resolved "https://registry.npmjs.org/@react-aria/selection/-/selection-3.21.0.tgz"
  integrity sha512-52JJ6hlPcM+gt0VV3DBmz6Kj1YAJr13TfutrKfGWcK36LvNCBm1j0N+TDqbdnlp8Nue6w0+5FIwZq44XPYiBGg==
  dependencies:
    "@react-aria/focus" "^3.19.0"
    "@react-aria/i18n" "^3.12.4"
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/selection" "^3.18.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/selection@^3.23.0":
  version "3.23.0"
  resolved "https://registry.npmjs.org/@react-aria/selection/-/selection-3.23.0.tgz"
  integrity sha512-m/sq3UuaTFRiEU9S6K+nkn9ONcpCtFskeJH/IZ9l/583X08KEoW/A3Vehrf3dlL8CNbkKKPfkUdKh1X6gTmHzA==
  dependencies:
    "@react-aria/focus" "^3.20.0"
    "@react-aria/i18n" "^3.12.6"
    "@react-aria/interactions" "^3.24.0"
    "@react-aria/utils" "^3.28.0"
    "@react-stately/selection" "^3.20.0"
    "@react-types/shared" "^3.28.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/slider@3.7.14":
  version "3.7.14"
  resolved "https://registry.npmjs.org/@react-aria/slider/-/slider-3.7.14.tgz"
  integrity sha512-7rOiKjLkEZ0j7mPMlwrqivc+K4OSfL14slaQp06GHRiJkhiWXh2/drPe15hgNq55HmBQBpA0umKMkJcqVgmXPA==
  dependencies:
    "@react-aria/focus" "^3.19.0"
    "@react-aria/i18n" "^3.12.4"
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/label" "^3.7.13"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/slider" "^3.6.0"
    "@react-types/shared" "^3.26.0"
    "@react-types/slider" "^3.7.7"
    "@swc/helpers" "^0.5.0"

"@react-aria/spinbutton@^3.6.10":
  version "3.6.12"
  resolved "https://registry.npmjs.org/@react-aria/spinbutton/-/spinbutton-3.6.12.tgz"
  integrity sha512-MtYYWl6wvUv+sUcEucTiHMoSRs2GsSNh+awEBJ5/boqQKU+bLjZ/9j/qIJO8Iueet2535HtLMKz1IsM0Pltrng==
  dependencies:
    "@react-aria/i18n" "^3.12.6"
    "@react-aria/live-announcer" "^3.4.1"
    "@react-aria/utils" "^3.28.0"
    "@react-types/button" "^3.11.0"
    "@react-types/shared" "^3.28.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/ssr@^3.9.7", "@react-aria/ssr@3.9.7":
  version "3.9.7"
  resolved "https://registry.npmjs.org/@react-aria/ssr/-/ssr-3.9.7.tgz"
  integrity sha512-GQygZaGlmYjmYM+tiNBA5C6acmiDWF52Nqd40bBp0Znk4M4hP+LTmI0lpI1BuKMw45T8RIhrAsICIfKwZvi2Gg==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@react-aria/switch@3.6.10":
  version "3.6.10"
  resolved "https://registry.npmjs.org/@react-aria/switch/-/switch-3.6.10.tgz"
  integrity sha512-FtaI9WaEP1tAmra1sYlAkYXg9x75P5UtgY8pSbe9+1WRyWbuE1QZT+RNCTi3IU4fZ7iJQmXH6+VaMyzPlSUagw==
  dependencies:
    "@react-aria/toggle" "^3.10.10"
    "@react-stately/toggle" "^3.8.0"
    "@react-types/shared" "^3.26.0"
    "@react-types/switch" "^3.5.7"
    "@swc/helpers" "^0.5.0"

"@react-aria/table@3.16.0":
  version "3.16.0"
  resolved "https://registry.npmjs.org/@react-aria/table/-/table-3.16.0.tgz"
  integrity sha512-9xF9S3CJ7XRiiK92hsIKxPedD0kgcQWwqTMtj3IBynpQ4vsnRiW3YNIzrn9C3apjknRZDTSta8O2QPYCUMmw2A==
  dependencies:
    "@react-aria/focus" "^3.19.0"
    "@react-aria/grid" "^3.11.0"
    "@react-aria/i18n" "^3.12.4"
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/live-announcer" "^3.4.1"
    "@react-aria/utils" "^3.26.0"
    "@react-aria/visually-hidden" "^3.8.18"
    "@react-stately/collections" "^3.12.0"
    "@react-stately/flags" "^3.0.5"
    "@react-stately/table" "^3.13.0"
    "@react-types/checkbox" "^3.9.0"
    "@react-types/grid" "^3.2.10"
    "@react-types/shared" "^3.26.0"
    "@react-types/table" "^3.10.3"
    "@swc/helpers" "^0.5.0"

"@react-aria/tabs@3.9.8":
  version "3.9.8"
  resolved "https://registry.npmjs.org/@react-aria/tabs/-/tabs-3.9.8.tgz"
  integrity sha512-Nur/qRFBe+Zrt4xcCJV/ULXCS3Mlae+B89bp1Gl20vSDqk6uaPtGk+cS5k03eugOvas7AQapqNJsJgKd66TChw==
  dependencies:
    "@react-aria/focus" "^3.19.0"
    "@react-aria/i18n" "^3.12.4"
    "@react-aria/selection" "^3.21.0"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/tabs" "^3.7.0"
    "@react-types/shared" "^3.26.0"
    "@react-types/tabs" "^3.3.11"
    "@swc/helpers" "^0.5.0"

"@react-aria/textfield@^3.15.0", "@react-aria/textfield@3.15.0":
  version "3.15.0"
  resolved "https://registry.npmjs.org/@react-aria/textfield/-/textfield-3.15.0.tgz"
  integrity sha512-V5mg7y1OR6WXYHdhhm4FC7QyGc9TideVRDFij1SdOJrIo5IFB7lvwpOS0GmgwkVbtr71PTRMjZnNbrJUFU6VNA==
  dependencies:
    "@react-aria/focus" "^3.19.0"
    "@react-aria/form" "^3.0.11"
    "@react-aria/label" "^3.7.13"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/form" "^3.1.0"
    "@react-stately/utils" "^3.10.5"
    "@react-types/shared" "^3.26.0"
    "@react-types/textfield" "^3.10.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/toggle@^3.10.10":
  version "3.11.0"
  resolved "https://registry.npmjs.org/@react-aria/toggle/-/toggle-3.11.0.tgz"
  integrity sha512-LQcuGxkoHIb79phsGVzLVWlA25Uj14fRMEo4r/DRB9xE+IiOgO8g3gaA5oWNT3kpM898lTxaIv1yVxhWZEksrQ==
  dependencies:
    "@react-aria/interactions" "^3.24.0"
    "@react-aria/utils" "^3.28.0"
    "@react-stately/toggle" "^3.8.2"
    "@react-types/checkbox" "^3.9.2"
    "@react-types/shared" "^3.28.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/toolbar@3.0.0-beta.11":
  version "3.0.0-beta.11"
  resolved "https://registry.npmjs.org/@react-aria/toolbar/-/toolbar-3.0.0-beta.11.tgz"
  integrity sha512-LM3jTRFNDgoEpoL568WaiuqiVM7eynSQLJis1hV0vlVnhTd7M7kzt7zoOjzxVb5Uapz02uCp1Fsm4wQMz09qwQ==
  dependencies:
    "@react-aria/focus" "^3.19.0"
    "@react-aria/i18n" "^3.12.4"
    "@react-aria/utils" "^3.26.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/tooltip@3.7.10":
  version "3.7.10"
  resolved "https://registry.npmjs.org/@react-aria/tooltip/-/tooltip-3.7.10.tgz"
  integrity sha512-Udi3XOnrF/SYIz72jw9bgB74MG/yCOzF5pozHj2FH2HiJlchYv/b6rHByV/77IZemdlkmL/uugrv/7raPLSlnw==
  dependencies:
    "@react-aria/focus" "^3.19.0"
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/tooltip" "^3.5.0"
    "@react-types/shared" "^3.26.0"
    "@react-types/tooltip" "^3.4.13"
    "@swc/helpers" "^0.5.0"

"@react-aria/utils@^3.26.0", "@react-aria/utils@3.26.0":
  version "3.26.0"
  resolved "https://registry.npmjs.org/@react-aria/utils/-/utils-3.26.0.tgz"
  integrity sha512-LkZouGSjjQ0rEqo4XJosS4L3YC/zzQkfRM3KoqK6fUOmUJ9t0jQ09WjiF+uOoG9u+p30AVg3TrZRUWmoTS+koQ==
  dependencies:
    "@react-aria/ssr" "^3.9.7"
    "@react-stately/utils" "^3.10.5"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"
    clsx "^2.0.0"

"@react-aria/utils@^3.28.0":
  version "3.28.0"
  resolved "https://registry.npmjs.org/@react-aria/utils/-/utils-3.28.0.tgz"
  integrity sha512-FfpvpADk61OvEnFe37k6jF1zr5gtafIPN9ccJRnPCTqrzuExag01mGi+wX/hWyFK0zAe1OjWf1zFOX3FsFvikg==
  dependencies:
    "@react-aria/ssr" "^3.9.7"
    "@react-stately/flags" "^3.1.0"
    "@react-stately/utils" "^3.10.5"
    "@react-types/shared" "^3.28.0"
    "@swc/helpers" "^0.5.0"
    clsx "^2.0.0"

"@react-aria/visually-hidden@^3.8.18", "@react-aria/visually-hidden@3.8.18":
  version "3.8.18"
  resolved "https://registry.npmjs.org/@react-aria/visually-hidden/-/visually-hidden-3.8.18.tgz"
  integrity sha512-l/0igp+uub/salP35SsNWq5mGmg3G5F5QMS1gDZ8p28n7CgjvzyiGhJbbca7Oxvaw1HRFzVl9ev+89I7moNnFQ==
  dependencies:
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/utils" "^3.26.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-spring/animated@~9.7.5":
  version "9.7.5"
  resolved "https://registry.npmjs.org/@react-spring/animated/-/animated-9.7.5.tgz"
  integrity sha512-Tqrwz7pIlsSDITzxoLS3n/v/YCUHQdOIKtOJf4yL6kYVSDTSmVK1LI1Q3M/uu2Sx4X3pIWF3xLUhlsA6SPNTNg==
  dependencies:
    "@react-spring/shared" "~9.7.5"
    "@react-spring/types" "~9.7.5"

"@react-spring/core@~9.7.5":
  version "9.7.5"
  resolved "https://registry.npmjs.org/@react-spring/core/-/core-9.7.5.tgz"
  integrity sha512-rmEqcxRcu7dWh7MnCcMXLvrf6/SDlSokLaLTxiPlAYi11nN3B5oiCUAblO72o+9z/87j2uzxa2Inm8UbLjXA+w==
  dependencies:
    "@react-spring/animated" "~9.7.5"
    "@react-spring/shared" "~9.7.5"
    "@react-spring/types" "~9.7.5"

"@react-spring/rafz@^9.7.5", "@react-spring/rafz@~9.7.5":
  version "9.7.5"
  resolved "https://registry.npmjs.org/@react-spring/rafz/-/rafz-9.7.5.tgz"
  integrity sha512-5ZenDQMC48wjUzPAm1EtwQ5Ot3bLIAwwqP2w2owG5KoNdNHpEJV263nGhCeKKmuA3vG2zLLOdu3or6kuDjA6Aw==

"@react-spring/shared@~9.7.5":
  version "9.7.5"
  resolved "https://registry.npmjs.org/@react-spring/shared/-/shared-9.7.5.tgz"
  integrity sha512-wdtoJrhUeeyD/PP/zo+np2s1Z820Ohr/BbuVYv+3dVLW7WctoiN7std8rISoYoHpUXtbkpesSKuPIw/6U1w1Pw==
  dependencies:
    "@react-spring/rafz" "~9.7.5"
    "@react-spring/types" "~9.7.5"

"@react-spring/types@~9.7.5":
  version "9.7.5"
  resolved "https://registry.npmjs.org/@react-spring/types/-/types-9.7.5.tgz"
  integrity sha512-HVj7LrZ4ReHWBimBvu2SKND3cDVUPWKLqRTmWe/fNY6o1owGOX0cAHbdPDTMelgBlVbrTKrre6lFkhqGZErK/g==

"@react-spring/web@^9.7.5":
  version "9.7.5"
  resolved "https://registry.npmjs.org/@react-spring/web/-/web-9.7.5.tgz"
  integrity sha512-lmvqGwpe+CSttsWNZVr+Dg62adtKhauGwLyGE/RRyZ8AAMLgb9x3NDMA5RMElXo+IMyTkPp7nxTB8ZQlmhb6JQ==
  dependencies:
    "@react-spring/animated" "~9.7.5"
    "@react-spring/core" "~9.7.5"
    "@react-spring/shared" "~9.7.5"
    "@react-spring/types" "~9.7.5"

"@react-stately/calendar@^3.6.0", "@react-stately/calendar@3.6.0":
  version "3.6.0"
  resolved "https://registry.npmjs.org/@react-stately/calendar/-/calendar-3.6.0.tgz"
  integrity sha512-GqUtOtGnwWjtNrJud8nY/ywI4VBP5byToNVRTnxbMl+gYO1Qe/uc5NG7zjwMxhb2kqSBHZFdkF0DXVqG2Ul+BA==
  dependencies:
    "@internationalized/date" "^3.6.0"
    "@react-stately/utils" "^3.10.5"
    "@react-types/calendar" "^3.5.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/checkbox@^3.6.10", "@react-stately/checkbox@3.6.10":
  version "3.6.10"
  resolved "https://registry.npmjs.org/@react-stately/checkbox/-/checkbox-3.6.10.tgz"
  integrity sha512-LHm7i4YI8A/RdgWAuADrnSAYIaYYpQeZqsp1a03Og0pJHAlZL0ymN3y2IFwbZueY0rnfM+yF+kWNXjJqbKrFEQ==
  dependencies:
    "@react-stately/form" "^3.1.0"
    "@react-stately/utils" "^3.10.5"
    "@react-types/checkbox" "^3.9.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/collections@^3.12.0", "@react-stately/collections@3.12.0":
  version "3.12.0"
  resolved "https://registry.npmjs.org/@react-stately/collections/-/collections-3.12.0.tgz"
  integrity sha512-MfR9hwCxe5oXv4qrLUnjidwM50U35EFmInUeFf8i9mskYwWlRYS0O1/9PZ0oF1M0cKambaRHKEy98jczgb9ycA==
  dependencies:
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/collections@^3.12.2":
  version "3.12.2"
  resolved "https://registry.npmjs.org/@react-stately/collections/-/collections-3.12.2.tgz"
  integrity sha512-RoehfGwrsYJ/WGtyGSLZNYysszajnq0Q3iTXg7plfW1vNEzom/A31vrLjOSOHJWAtwW339SDGGRpymDtLo4GWA==
  dependencies:
    "@react-types/shared" "^3.28.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/combobox@^3.10.1", "@react-stately/combobox@3.10.1":
  version "3.10.1"
  resolved "https://registry.npmjs.org/@react-stately/combobox/-/combobox-3.10.1.tgz"
  integrity sha512-Rso+H+ZEDGFAhpKWbnRxRR/r7YNmYVtt+Rn0eNDNIUp3bYaxIBCdCySyAtALs4I8RZXZQ9zoUznP7YeVwG3cLg==
  dependencies:
    "@react-stately/collections" "^3.12.0"
    "@react-stately/form" "^3.1.0"
    "@react-stately/list" "^3.11.1"
    "@react-stately/overlays" "^3.6.12"
    "@react-stately/select" "^3.6.9"
    "@react-stately/utils" "^3.10.5"
    "@react-types/combobox" "^3.13.1"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/datepicker@^3.11.0", "@react-stately/datepicker@3.11.0":
  version "3.11.0"
  resolved "https://registry.npmjs.org/@react-stately/datepicker/-/datepicker-3.11.0.tgz"
  integrity sha512-d9MJF34A0VrhL5y5S8mAISA8uwfNCQKmR2k4KoQJm3De1J8SQeNzSjLviAwh1faDow6FXGlA6tVbTrHyDcBgBg==
  dependencies:
    "@internationalized/date" "^3.6.0"
    "@internationalized/string" "^3.2.5"
    "@react-stately/form" "^3.1.0"
    "@react-stately/overlays" "^3.6.12"
    "@react-stately/utils" "^3.10.5"
    "@react-types/datepicker" "^3.9.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/flags@^3.0.5", "@react-stately/flags@^3.1.0":
  version "3.1.0"
  resolved "https://registry.npmjs.org/@react-stately/flags/-/flags-3.1.0.tgz"
  integrity sha512-KSHOCxTFpBtxhIRcKwsD1YDTaNxFtCYuAUb0KEihc16QwqZViq4hasgPBs2gYm7fHRbw7WYzWKf6ZSo/+YsFlg==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@react-stately/form@^3.1.0", "@react-stately/form@3.1.0":
  version "3.1.0"
  resolved "https://registry.npmjs.org/@react-stately/form/-/form-3.1.0.tgz"
  integrity sha512-E2wxNQ0QaTyDHD0nJFtTSnEH9A3bpJurwxhS4vgcUmESHgjFEMLlC9irUSZKgvOgb42GAq+fHoWBsgKeTp9Big==
  dependencies:
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/form@^3.1.2":
  version "3.1.2"
  resolved "https://registry.npmjs.org/@react-stately/form/-/form-3.1.2.tgz"
  integrity sha512-sKgkV+rxeqM1lf0dCq2wWzdYa5Z0wz/MB3yxjodffy8D43PjFvUOMWpgw/752QHPGCd1XIxA3hE58Dw9FFValg==
  dependencies:
    "@react-types/shared" "^3.28.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/grid@^3.10.0", "@react-stately/grid@^3.11.0":
  version "3.11.0"
  resolved "https://registry.npmjs.org/@react-stately/grid/-/grid-3.11.0.tgz"
  integrity sha512-Wp6kza+2MzNybls9pRWvIwAHwMnSV1eUZXZxLwJy+JVS5lghkr731VvT+YD79z70osJKmgxgmiQGm4/yfetXdA==
  dependencies:
    "@react-stately/collections" "^3.12.2"
    "@react-stately/selection" "^3.20.0"
    "@react-types/grid" "^3.3.0"
    "@react-types/shared" "^3.28.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/list@^3.11.1", "@react-stately/list@3.11.1":
  version "3.11.1"
  resolved "https://registry.npmjs.org/@react-stately/list/-/list-3.11.1.tgz"
  integrity sha512-UCOpIvqBOjwLtk7zVTYWuKU1m1Oe61Q5lNar/GwHaV1nAiSQ8/yYlhr40NkBEs9X3plEfsV28UIpzOrYnu1tPg==
  dependencies:
    "@react-stately/collections" "^3.12.0"
    "@react-stately/selection" "^3.18.0"
    "@react-stately/utils" "^3.10.5"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/list@^3.12.0":
  version "3.12.0"
  resolved "https://registry.npmjs.org/@react-stately/list/-/list-3.12.0.tgz"
  integrity sha512-6niQWJ6TZwOKLAOn2wIsxtOvWenh3rKiKdOh4L4O4f7U+h1Hu000Mu4lyIQm2P9uZAkF2Y5QNh6dHN+hSd6h3A==
  dependencies:
    "@react-stately/collections" "^3.12.2"
    "@react-stately/selection" "^3.20.0"
    "@react-stately/utils" "^3.10.5"
    "@react-types/shared" "^3.28.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/menu@^3.9.0", "@react-stately/menu@3.9.0":
  version "3.9.0"
  resolved "https://registry.npmjs.org/@react-stately/menu/-/menu-3.9.0.tgz"
  integrity sha512-++sm0fzZeUs9GvtRbj5RwrP+KL9KPANp9f4SvtI3s+MP+Y/X3X7LNNePeeccGeyikB5fzMsuyvd82bRRW9IhDQ==
  dependencies:
    "@react-stately/overlays" "^3.6.12"
    "@react-types/menu" "^3.9.13"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/overlays@^3.6.12", "@react-stately/overlays@3.6.12":
  version "3.6.12"
  resolved "https://registry.npmjs.org/@react-stately/overlays/-/overlays-3.6.12.tgz"
  integrity sha512-QinvZhwZgj8obUyPIcyURSCjTZlqZYRRCS60TF8jH8ZpT0tEAuDb3wvhhSXuYA3Xo9EHLwvLjEf3tQKKdAQArw==
  dependencies:
    "@react-stately/utils" "^3.10.5"
    "@react-types/overlays" "^3.8.11"
    "@swc/helpers" "^0.5.0"

"@react-stately/overlays@^3.6.14":
  version "3.6.14"
  resolved "https://registry.npmjs.org/@react-stately/overlays/-/overlays-3.6.14.tgz"
  integrity sha512-RRalTuHdwrKO1BmXKaqBtE1GGUXU4VUAWwgh4lsP2EFSixDHmOVLxHFDWYvOPChBhpi8KXfLEgm6DEgPBvLBZQ==
  dependencies:
    "@react-stately/utils" "^3.10.5"
    "@react-types/overlays" "^3.8.13"
    "@swc/helpers" "^0.5.0"

"@react-stately/radio@^3.10.9", "@react-stately/radio@3.10.9":
  version "3.10.9"
  resolved "https://registry.npmjs.org/@react-stately/radio/-/radio-3.10.9.tgz"
  integrity sha512-kUQ7VdqFke8SDRCatw2jW3rgzMWbvw+n2imN2THETynI47NmNLzNP11dlGO2OllRtTrsLhmBNlYHa3W62pFpAw==
  dependencies:
    "@react-stately/form" "^3.1.0"
    "@react-stately/utils" "^3.10.5"
    "@react-types/radio" "^3.8.5"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/select@^3.6.9":
  version "3.6.11"
  resolved "https://registry.npmjs.org/@react-stately/select/-/select-3.6.11.tgz"
  integrity sha512-8pD4PNbZQNWg33D4+Fa0mrajUCYV3aA5YIwW3GY8NSRwBspaW4PKSZJtDT5ieN0WAO44YkAmX4idRaMAvqRusA==
  dependencies:
    "@react-stately/form" "^3.1.2"
    "@react-stately/list" "^3.12.0"
    "@react-stately/overlays" "^3.6.14"
    "@react-types/select" "^3.9.10"
    "@react-types/shared" "^3.28.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/selection@^3.18.0", "@react-stately/selection@^3.20.0":
  version "3.20.0"
  resolved "https://registry.npmjs.org/@react-stately/selection/-/selection-3.20.0.tgz"
  integrity sha512-woUSHMTyQiNmCf63Dyot1WXFfWnm6PFYkI9kymcq1qrrly4g/j27U+5PaRWOHawMiJwn1e1GTogk8B+K5ahshQ==
  dependencies:
    "@react-stately/collections" "^3.12.2"
    "@react-stately/utils" "^3.10.5"
    "@react-types/shared" "^3.28.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/slider@^3.6.0", "@react-stately/slider@3.6.0":
  version "3.6.0"
  resolved "https://registry.npmjs.org/@react-stately/slider/-/slider-3.6.0.tgz"
  integrity sha512-w5vJxVh267pmD1X+Ppd9S3ZzV1hcg0cV8q5P4Egr160b9WMcWlUspZPtsthwUlN7qQe/C8y5IAhtde4s29eNag==
  dependencies:
    "@react-stately/utils" "^3.10.5"
    "@react-types/shared" "^3.26.0"
    "@react-types/slider" "^3.7.7"
    "@swc/helpers" "^0.5.0"

"@react-stately/table@^3.13.0", "@react-stately/table@3.13.0":
  version "3.13.0"
  resolved "https://registry.npmjs.org/@react-stately/table/-/table-3.13.0.tgz"
  integrity sha512-mRbNYrwQIE7xzVs09Lk3kPteEVFVyOc20vA8ph6EP54PiUf/RllJpxZe/WUYLf4eom9lUkRYej5sffuUBpxjCA==
  dependencies:
    "@react-stately/collections" "^3.12.0"
    "@react-stately/flags" "^3.0.5"
    "@react-stately/grid" "^3.10.0"
    "@react-stately/selection" "^3.18.0"
    "@react-stately/utils" "^3.10.5"
    "@react-types/grid" "^3.2.10"
    "@react-types/shared" "^3.26.0"
    "@react-types/table" "^3.10.3"
    "@swc/helpers" "^0.5.0"

"@react-stately/tabs@^3.7.0", "@react-stately/tabs@3.7.0":
  version "3.7.0"
  resolved "https://registry.npmjs.org/@react-stately/tabs/-/tabs-3.7.0.tgz"
  integrity sha512-ox4hTkfZCoR4Oyr3Op3rBlWNq2Wxie04vhEYpTZQ2hobR3l4fYaOkd7CPClILktJ3TC104j8wcb0knWxIBRx9w==
  dependencies:
    "@react-stately/list" "^3.11.1"
    "@react-types/shared" "^3.26.0"
    "@react-types/tabs" "^3.3.11"
    "@swc/helpers" "^0.5.0"

"@react-stately/toggle@^3.8.0", "@react-stately/toggle@3.8.0":
  version "3.8.0"
  resolved "https://registry.npmjs.org/@react-stately/toggle/-/toggle-3.8.0.tgz"
  integrity sha512-pyt/k/J8BwE/2g6LL6Z6sMSWRx9HEJB83Sm/MtovXnI66sxJ2EfQ1OaXB7Su5PEL9OMdoQF6Mb+N1RcW3zAoPw==
  dependencies:
    "@react-stately/utils" "^3.10.5"
    "@react-types/checkbox" "^3.9.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/toggle@^3.8.2":
  version "3.8.2"
  resolved "https://registry.npmjs.org/@react-stately/toggle/-/toggle-3.8.2.tgz"
  integrity sha512-5KPpT6zvt8H+WC9UbubhCTZltREeYb/3hKdl4YkS7BbSOQlHTFC0pOk8SsQU70Pwk26jeVHbl5le/N8cw00x8w==
  dependencies:
    "@react-stately/utils" "^3.10.5"
    "@react-types/checkbox" "^3.9.2"
    "@react-types/shared" "^3.28.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/tooltip@^3.5.0", "@react-stately/tooltip@3.5.0":
  version "3.5.0"
  resolved "https://registry.npmjs.org/@react-stately/tooltip/-/tooltip-3.5.0.tgz"
  integrity sha512-+xzPNztJDd2XJD0X3DgWKlrgOhMqZpSzsIssXeJgO7uCnP8/Z513ESaipJhJCFC8fxj5caO/DK4Uu8hEtlB8cQ==
  dependencies:
    "@react-stately/overlays" "^3.6.12"
    "@react-types/tooltip" "^3.4.13"
    "@swc/helpers" "^0.5.0"

"@react-stately/tree@^3.8.6", "@react-stately/tree@3.8.6":
  version "3.8.6"
  resolved "https://registry.npmjs.org/@react-stately/tree/-/tree-3.8.6.tgz"
  integrity sha512-lblUaxf1uAuIz5jm6PYtcJ+rXNNVkqyFWTIMx6g6gW/mYvm8GNx1G/0MLZE7E6CuDGaO9dkLSY2bB1uqyKHidA==
  dependencies:
    "@react-stately/collections" "^3.12.0"
    "@react-stately/selection" "^3.18.0"
    "@react-stately/utils" "^3.10.5"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/utils@^3.10.5", "@react-stately/utils@3.10.5":
  version "3.10.5"
  resolved "https://registry.npmjs.org/@react-stately/utils/-/utils-3.10.5.tgz"
  integrity sha512-iMQSGcpaecghDIh3mZEpZfoFH3ExBwTtuBEcvZ2XnGzCgQjeYXcMdIUwAfVQLXFTdHUHGF6Gu6/dFrYsCzySBQ==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@react-stately/virtualizer@4.2.0":
  version "4.2.0"
  resolved "https://registry.npmjs.org/@react-stately/virtualizer/-/virtualizer-4.2.0.tgz"
  integrity sha512-aTMpa9AQoz/xLqn8AI1BR/caUUY7/OUo9GbuF434w2u5eGCL7+SAn3Fmq7WSCwqYyDsO+jEIERek4JTX7pEW0A==
  dependencies:
    "@react-aria/utils" "^3.26.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-types/accordion@3.0.0-alpha.25":
  version "3.0.0-alpha.25"
  resolved "https://registry.npmjs.org/@react-types/accordion/-/accordion-3.0.0-alpha.25.tgz"
  integrity sha512-nPTRrMA5jS4QcwQ0H8J9Tzzw7+yq+KbwsPNA1ukVIfOGIB45by/1ke/eiZAXGqXxkElxi2fQuaXuWm79BWZ8zg==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/breadcrumbs@^3.7.9", "@react-types/breadcrumbs@3.7.9":
  version "3.7.9"
  resolved "https://registry.npmjs.org/@react-types/breadcrumbs/-/breadcrumbs-3.7.9.tgz"
  integrity sha512-eARYJo8J+VfNV8vP4uw3L2Qliba9wLV2bx9YQCYf5Lc/OE5B/y4gaTLz+Y2P3Rtn6gBPLXY447zCs5i7gf+ICg==
  dependencies:
    "@react-types/link" "^3.5.9"
    "@react-types/shared" "^3.26.0"

"@react-types/button@^3.10.1", "@react-types/button@3.10.1":
  version "3.10.1"
  resolved "https://registry.npmjs.org/@react-types/button/-/button-3.10.1.tgz"
  integrity sha512-XTtap8o04+4QjPNAshFWOOAusUTxQlBjU2ai0BTVLShQEjHhRVDBIWsI2B2FKJ4KXT6AZ25llaxhNrreWGonmA==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/button@^3.11.0":
  version "3.11.0"
  resolved "https://registry.npmjs.org/@react-types/button/-/button-3.11.0.tgz"
  integrity sha512-gJh5i0JiBiZGZGDo+tXMp6xbixPM7IKZ0sDuxTYBG49qNzzWJq0uNYltO3emwSVXFSsBgRV/Wu8kQGhfuN7wIw==
  dependencies:
    "@react-types/shared" "^3.28.0"

"@react-types/calendar@^3.5.0", "@react-types/calendar@3.5.0":
  version "3.5.0"
  resolved "https://registry.npmjs.org/@react-types/calendar/-/calendar-3.5.0.tgz"
  integrity sha512-O3IRE7AGwAWYnvJIJ80cOy7WwoJ0m8GtX/qSmvXQAjC4qx00n+b5aFNBYAQtcyc3RM5QpW6obs9BfwGetFiI8w==
  dependencies:
    "@internationalized/date" "^3.6.0"
    "@react-types/shared" "^3.26.0"

"@react-types/checkbox@^3.9.0", "@react-types/checkbox@3.9.0":
  version "3.9.0"
  resolved "https://registry.npmjs.org/@react-types/checkbox/-/checkbox-3.9.0.tgz"
  integrity sha512-9hbHx0Oo2Hp5a8nV8Q75LQR0DHtvOIJbFaeqESSopqmV9EZoYjtY/h0NS7cZetgahQgnqYWQi44XGooMDCsmxA==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/checkbox@^3.9.2":
  version "3.9.2"
  resolved "https://registry.npmjs.org/@react-types/checkbox/-/checkbox-3.9.2.tgz"
  integrity sha512-BruOLjr9s0BS2+G1Q2ZZ0ubnSTG54hZWr59lCHXaLxMdA/+KVsR6JVMQuYKsW0P8RDDlQXE/QGz3n9yB/Ara4A==
  dependencies:
    "@react-types/shared" "^3.28.0"

"@react-types/combobox@^3.13.1", "@react-types/combobox@3.13.1":
  version "3.13.1"
  resolved "https://registry.npmjs.org/@react-types/combobox/-/combobox-3.13.1.tgz"
  integrity sha512-7xr+HknfhReN4QPqKff5tbKTe2kGZvH+DGzPYskAtb51FAAiZsKo+WvnNAvLwg3kRoC9Rkn4TAiVBp/HgymRDw==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/datepicker@^3.9.0", "@react-types/datepicker@3.9.0":
  version "3.9.0"
  resolved "https://registry.npmjs.org/@react-types/datepicker/-/datepicker-3.9.0.tgz"
  integrity sha512-dbKL5Qsm2MQwOTtVQdOcKrrphcXAqDD80WLlSQrBLg+waDuuQ7H+TrvOT0thLKloNBlFUGnZZfXGRHINpih/0g==
  dependencies:
    "@internationalized/date" "^3.6.0"
    "@react-types/calendar" "^3.5.0"
    "@react-types/overlays" "^3.8.11"
    "@react-types/shared" "^3.26.0"

"@react-types/dialog@^3.5.14":
  version "3.5.16"
  resolved "https://registry.npmjs.org/@react-types/dialog/-/dialog-3.5.16.tgz"
  integrity sha512-2D16XjuW9fG3LkVIXu3RzUp3zcK2IZOWlAl+r5i0aLw2Q0QHyYMfGbmgvhxVeAhxhEj/57/ziSl/8rJ9pzmFnw==
  dependencies:
    "@react-types/overlays" "^3.8.13"
    "@react-types/shared" "^3.28.0"

"@react-types/form@3.7.8":
  version "3.7.8"
  resolved "https://registry.npmjs.org/@react-types/form/-/form-3.7.8.tgz"
  integrity sha512-0wOS97/X0ijTVuIqik1lHYTZnk13QkvMTKvIEhM7c6YMU3vPiirBwLbT2kJiAdwLiymwcCkrBdDF1NTRG6kPFA==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/grid@^3.2.10", "@react-types/grid@3.2.10":
  version "3.2.10"
  resolved "https://registry.npmjs.org/@react-types/grid/-/grid-3.2.10.tgz"
  integrity sha512-Z5cG0ITwqjUE4kWyU5/7VqiPl4wqMJ7kG/ZP7poAnLmwRsR8Ai0ceVn+qzp5nTA19cgURi8t3LsXn3Ar1FBoog==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/grid@^3.3.0":
  version "3.3.0"
  resolved "https://registry.npmjs.org/@react-types/grid/-/grid-3.3.0.tgz"
  integrity sha512-9IXgD5qXXxz+S9RK+zT8umuTCEcE4Yfdl0zUGyTCB8LVcPEeZuarLGXZY/12Rkbd8+r6MUIKTxMVD3Nq9X5Ksg==
  dependencies:
    "@react-types/shared" "^3.28.0"

"@react-types/link@^3.5.9", "@react-types/link@3.5.9":
  version "3.5.9"
  resolved "https://registry.npmjs.org/@react-types/link/-/link-3.5.9.tgz"
  integrity sha512-JcKDiDMqrq/5Vpn+BdWQEuXit4KN4HR/EgIi3yKnNbYkLzxBoeQZpQgvTaC7NEQeZnSqkyXQo3/vMUeX/ZNIKw==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/listbox@^3.5.3":
  version "3.5.5"
  resolved "https://registry.npmjs.org/@react-types/listbox/-/listbox-3.5.5.tgz"
  integrity sha512-6cUjbYZVa0X2UMsenQ50ZaAssTUfzX3D0Q0Wd5nNf4W7ntBroDg6aBfNQoPDZikPUy8u+Y3uc/xZQfv30si7NA==
  dependencies:
    "@react-types/shared" "^3.28.0"

"@react-types/menu@^3.9.13", "@react-types/menu@3.9.13":
  version "3.9.13"
  resolved "https://registry.npmjs.org/@react-types/menu/-/menu-3.9.13.tgz"
  integrity sha512-7SuX6E2tDsqQ+HQdSvIda1ji/+ujmR86dtS9CUu5yWX91P25ufRjZ72EvLRqClWNQsj1Xl4+2zBDLWlceznAjw==
  dependencies:
    "@react-types/overlays" "^3.8.11"
    "@react-types/shared" "^3.26.0"

"@react-types/overlays@^3.8.11", "@react-types/overlays@3.8.11":
  version "3.8.11"
  resolved "https://registry.npmjs.org/@react-types/overlays/-/overlays-3.8.11.tgz"
  integrity sha512-aw7T0rwVI3EuyG5AOaEIk8j7dZJQ9m34XAztXJVZ/W2+4pDDkLDbJ/EAPnuo2xGYRGhowuNDn4tDju01eHYi+w==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/overlays@^3.8.13":
  version "3.8.13"
  resolved "https://registry.npmjs.org/@react-types/overlays/-/overlays-3.8.13.tgz"
  integrity sha512-xgT843KIh1otvYPQ6kCGTVUICiMF5UQ7SZUQZd4Zk3VtiFIunFVUvTvL03cpt0026UmY7tbv7vFrPKcT6xjsjw==
  dependencies:
    "@react-types/shared" "^3.28.0"

"@react-types/progress@^3.5.8", "@react-types/progress@3.5.8":
  version "3.5.8"
  resolved "https://registry.npmjs.org/@react-types/progress/-/progress-3.5.8.tgz"
  integrity sha512-PR0rN5mWevfblR/zs30NdZr+82Gka/ba7UHmYOW9/lkKlWeD7PHgl1iacpd/3zl/jUF22evAQbBHmk1mS6Mpqw==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/radio@^3.8.5", "@react-types/radio@3.8.5":
  version "3.8.5"
  resolved "https://registry.npmjs.org/@react-types/radio/-/radio-3.8.5.tgz"
  integrity sha512-gSImTPid6rsbJmwCkTliBIU/npYgJHOFaI3PNJo7Y0QTAnFelCtYeFtBiWrFodSArSv7ASqpLLUEj9hZu/rxIg==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/select@^3.9.10":
  version "3.9.10"
  resolved "https://registry.npmjs.org/@react-types/select/-/select-3.9.10.tgz"
  integrity sha512-vvC5+cBSOu6J6lm74jhhP3Zvo1JO8m0FNX+Q95wapxrhs2aYYeMIgVuvNKeOuhVqzpBZxWmblBjCVNzCArZOaQ==
  dependencies:
    "@react-types/shared" "^3.28.0"

"@react-types/select@3.9.8":
  version "3.9.8"
  resolved "https://registry.npmjs.org/@react-types/select/-/select-3.9.8.tgz"
  integrity sha512-RGsYj2oFjXpLnfcvWMBQnkcDuKkwT43xwYWZGI214/gp/B64tJiIUgTM5wFTRAeGDX23EePkhCQF+9ctnqFd6g==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/shared@^3.26.0", "@react-types/shared@3.26.0":
  version "3.26.0"
  resolved "https://registry.npmjs.org/@react-types/shared/-/shared-3.26.0.tgz"
  integrity sha512-6FuPqvhmjjlpEDLTiYx29IJCbCNWPlsyO+ZUmCUXzhUv2ttShOXfw8CmeHWHftT/b2KweAWuzqSlfeXPR76jpw==

"@react-types/shared@^3.28.0":
  version "3.28.0"
  resolved "https://registry.npmjs.org/@react-types/shared/-/shared-3.28.0.tgz"
  integrity sha512-9oMEYIDc3sk0G5rysnYvdNrkSg7B04yTKl50HHSZVbokeHpnU0yRmsDaWb9B/5RprcKj8XszEk5guBO8Sa/Q+Q==

"@react-types/slider@^3.7.7":
  version "3.7.9"
  resolved "https://registry.npmjs.org/@react-types/slider/-/slider-3.7.9.tgz"
  integrity sha512-MxCIVkrBSbN3AxIYW4hOpTcwPmIuY4841HF36sDLFWR3wx06z70IY3GFwV7Cbp814vhc84d4ABnPMwtE+AZRGQ==
  dependencies:
    "@react-types/shared" "^3.28.0"

"@react-types/switch@^3.5.7":
  version "3.5.9"
  resolved "https://registry.npmjs.org/@react-types/switch/-/switch-3.5.9.tgz"
  integrity sha512-7XIS5qycIKhdfcWfzl8n458/7tkZKCNfMfZmIREgozKOtTBirjmtRRsefom2hlFT8VIlG7COmY4btK3oEuEhnQ==
  dependencies:
    "@react-types/shared" "^3.28.0"

"@react-types/table@^3.10.3", "@react-types/table@3.10.3":
  version "3.10.3"
  resolved "https://registry.npmjs.org/@react-types/table/-/table-3.10.3.tgz"
  integrity sha512-Ac+W+m/zgRzlTU8Z2GEg26HkuJFswF9S6w26r+R3MHwr8z2duGPvv37XRtE1yf3dbpRBgHEAO141xqS2TqGwNg==
  dependencies:
    "@react-types/grid" "^3.2.10"
    "@react-types/shared" "^3.26.0"

"@react-types/tabs@^3.3.11", "@react-types/tabs@3.3.11":
  version "3.3.11"
  resolved "https://registry.npmjs.org/@react-types/tabs/-/tabs-3.3.11.tgz"
  integrity sha512-BjF2TqBhZaIcC4lc82R5pDJd1F7kstj1K0Nokhz99AGYn8C0ITdp6lR+DPVY9JZRxKgP9R2EKfWGI90Lo7NQdA==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/textfield@^3.10.0", "@react-types/textfield@3.10.0":
  version "3.10.0"
  resolved "https://registry.npmjs.org/@react-types/textfield/-/textfield-3.10.0.tgz"
  integrity sha512-ShU3d6kLJGQjPXccVFjM3KOXdj3uyhYROqH9YgSIEVxgA9W6LRflvk/IVBamD9pJYTPbwmVzuP0wQkTDupfZ1w==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/tooltip@^3.4.13", "@react-types/tooltip@3.4.13":
  version "3.4.13"
  resolved "https://registry.npmjs.org/@react-types/tooltip/-/tooltip-3.4.13.tgz"
  integrity sha512-KPekFC17RTT8kZlk7ZYubueZnfsGTDOpLw7itzolKOXGddTXsrJGBzSB4Bb060PBVllaDO0MOrhPap8OmrIl1Q==
  dependencies:
    "@react-types/overlays" "^3.8.11"
    "@react-types/shared" "^3.26.0"

"@rtsao/scc@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@rtsao/scc/-/scc-1.1.0.tgz"
  integrity sha512-zt6OdqaDoOnJ1ZYsCYGt9YmWzDXl4vQdKTyJev62gFhRGKdx7mcT54V9KIjg+d2wi9EXsPvAPKe7i7WjfVWB8g==

"@rushstack/eslint-patch@^1.3.3":
  version "1.10.5"
  resolved "https://registry.npmjs.org/@rushstack/eslint-patch/-/eslint-patch-1.10.5.tgz"
  integrity sha512-kkKUDVlII2DQiKy7UstOR1ErJP8kUKAQ4oa+SQtM0K+lPdmmjj0YnnxBgtTVYH7mUKtbsxeFC9y0AmK7Yb78/A==

"@shadcn/ui@^0.0.4":
  version "0.0.4"
  resolved "https://registry.npmjs.org/@shadcn/ui/-/ui-0.0.4.tgz"
  integrity sha512-0dtu/5ApsOZ24qgaZwtif8jVwqol7a4m1x5AxPuM1k5wxhqU7t/qEfBGtaSki1R8VlbTQfCj5PAlO45NKCa7Gg==
  dependencies:
    chalk "5.2.0"
    commander "^10.0.0"
    execa "^7.0.0"
    fs-extra "^11.1.0"
    node-fetch "^3.3.0"
    ora "^6.1.2"
    prompts "^2.4.2"
    zod "^3.20.2"

"@supabase/auth-helpers-nextjs@^0.8.7":
  version "0.8.7"
  resolved "https://registry.npmjs.org/@supabase/auth-helpers-nextjs/-/auth-helpers-nextjs-0.8.7.tgz"
  integrity sha512-iYdOjFo0GkRvha340l8JdCiBiyXQuG9v8jnq7qMJ/2fakrskRgHTCOt7ryWbip1T6BExcWKC8SoJrhCzPOxhhg==
  dependencies:
    "@supabase/auth-helpers-shared" "0.6.3"
    set-cookie-parser "^2.6.0"

"@supabase/auth-helpers-shared@0.6.3":
  version "0.6.3"
  resolved "https://registry.npmjs.org/@supabase/auth-helpers-shared/-/auth-helpers-shared-0.6.3.tgz"
  integrity sha512-xYQRLFeFkL4ZfwC7p9VKcarshj3FB2QJMgJPydvOY7J5czJe6xSG5/wM1z63RmAzGbCkKg+dzpq61oeSyWiGBQ==
  dependencies:
    jose "^4.14.4"

"@supabase/auth-js@2.68.0":
  version "2.68.0"
  resolved "https://registry.npmjs.org/@supabase/auth-js/-/auth-js-2.68.0.tgz"
  integrity sha512-odG7nb7aOmZPUXk6SwL2JchSsn36Ppx11i2yWMIc/meUO2B2HK9YwZHPK06utD9Ql9ke7JKDbwGin/8prHKxxQ==
  dependencies:
    "@supabase/node-fetch" "^2.6.14"

"@supabase/functions-js@2.4.4":
  version "2.4.4"
  resolved "https://registry.npmjs.org/@supabase/functions-js/-/functions-js-2.4.4.tgz"
  integrity sha512-WL2p6r4AXNGwop7iwvul2BvOtuJ1YQy8EbOd0dhG1oN1q8el/BIRSFCFnWAMM/vJJlHWLi4ad22sKbKr9mvjoA==
  dependencies:
    "@supabase/node-fetch" "^2.6.14"

"@supabase/node-fetch@^2.6.14", "@supabase/node-fetch@2.6.15":
  version "2.6.15"
  resolved "https://registry.npmjs.org/@supabase/node-fetch/-/node-fetch-2.6.15.tgz"
  integrity sha512-1ibVeYUacxWYi9i0cf5efil6adJ9WRyZBLivgjs+AUpewx1F3xPi7gLgaASI2SmIQxPoCEjAsLAzKPgMJVgOUQ==
  dependencies:
    whatwg-url "^5.0.0"

"@supabase/postgrest-js@^1.1.1", "@supabase/postgrest-js@1.19.2":
  version "1.19.2"
  resolved "https://registry.npmjs.org/@supabase/postgrest-js/-/postgrest-js-1.19.2.tgz"
  integrity sha512-MXRbk4wpwhWl9IN6rIY1mR8uZCCG4MZAEji942ve6nMwIqnBgBnZhZlON6zTTs6fgveMnoCILpZv1+K91jN+ow==
  dependencies:
    "@supabase/node-fetch" "^2.6.14"

"@supabase/realtime-js@2.11.2":
  version "2.11.2"
  resolved "https://registry.npmjs.org/@supabase/realtime-js/-/realtime-js-2.11.2.tgz"
  integrity sha512-u/XeuL2Y0QEhXSoIPZZwR6wMXgB+RQbJzG9VErA3VghVt7uRfSVsjeqd7m5GhX3JR6dM/WRmLbVR8URpDWG4+w==
  dependencies:
    "@supabase/node-fetch" "^2.6.14"
    "@types/phoenix" "^1.5.4"
    "@types/ws" "^8.5.10"
    ws "^8.18.0"

"@supabase/storage-js@2.7.1":
  version "2.7.1"
  resolved "https://registry.npmjs.org/@supabase/storage-js/-/storage-js-2.7.1.tgz"
  integrity sha512-asYHcyDR1fKqrMpytAS1zjyEfvxuOIp1CIXX7ji4lHHcJKqyk+sLl/Vxgm4sN6u8zvuUtae9e4kDxQP2qrwWBA==
  dependencies:
    "@supabase/node-fetch" "^2.6.14"

"@supabase/supabase-js@^2.10.0", "@supabase/supabase-js@^2.19.0", "@supabase/supabase-js@^2.48.1":
  version "2.49.1"
  resolved "https://registry.npmjs.org/@supabase/supabase-js/-/supabase-js-2.49.1.tgz"
  integrity sha512-lKaptKQB5/juEF5+jzmBeZlz69MdHZuxf+0f50NwhL+IE//m4ZnOeWlsKRjjsM0fVayZiQKqLvYdBn0RLkhGiQ==
  dependencies:
    "@supabase/auth-js" "2.68.0"
    "@supabase/functions-js" "2.4.4"
    "@supabase/node-fetch" "2.6.15"
    "@supabase/postgrest-js" "1.19.2"
    "@supabase/realtime-js" "2.11.2"
    "@supabase/storage-js" "2.7.1"

"@swc/counter@^0.1.3":
  version "0.1.3"
  resolved "https://registry.npmjs.org/@swc/counter/-/counter-0.1.3.tgz"
  integrity sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==

"@swc/helpers@^0.5.0":
  version "0.5.15"
  resolved "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.15.tgz"
  integrity sha512-JQ5TuMi45Owi4/BIMAJBoSQoOJu12oOk/gADqlcUL9JEdHB8vyjUSsxqeNXnmXHjYKMi2WcYtezGEEhqUI/E2g==
  dependencies:
    tslib "^2.8.0"

"@swc/helpers@0.5.5":
  version "0.5.5"
  resolved "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.5.tgz"
  integrity sha512-KGYxvIOXcceOAbEk4bi/dVLEK9z8sZ0uBB3Il5b1rhfClSpcX0yfRO0KmTkqR2cnQDymwLB+25ZyMzICg/cm/A==
  dependencies:
    "@swc/counter" "^0.1.3"
    tslib "^2.4.0"

"@tanstack/query-core@5.67.2":
  version "5.67.2"
  resolved "https://registry.npmjs.org/@tanstack/query-core/-/query-core-5.67.2.tgz"
  integrity sha512-+iaFJ/pt8TaApCk6LuZ0WHS/ECVfTzrxDOEL9HH9Dayyb5OVuomLzDXeSaI2GlGT/8HN7bDGiRXDts3LV+u6ww==

"@tanstack/react-query@^5.17.15":
  version "5.67.2"
  resolved "https://registry.npmjs.org/@tanstack/react-query/-/react-query-5.67.2.tgz"
  integrity sha512-6Sa+BVNJWhAV4QHvIqM73norNeGRWGC3ftN0Ix87cmMvI215I1wyJ44KUTt/9a0V9YimfGcg25AITaYVel71Og==
  dependencies:
    "@tanstack/query-core" "5.67.2"

"@tanstack/react-table@^8.20.1":
  version "8.21.2"
  resolved "https://registry.npmjs.org/@tanstack/react-table/-/react-table-8.21.2.tgz"
  integrity sha512-11tNlEDTdIhMJba2RBH+ecJ9l1zgS2kjmexDPAraulc8jeNA4xocSNeyzextT0XJyASil4XsCYlJmf5jEWAtYg==
  dependencies:
    "@tanstack/table-core" "8.21.2"

"@tanstack/react-virtual@3.11.2":
  version "3.11.2"
  resolved "https://registry.npmjs.org/@tanstack/react-virtual/-/react-virtual-3.11.2.tgz"
  integrity sha512-OuFzMXPF4+xZgx8UzJha0AieuMihhhaWG0tCqpp6tDzlFwOmNBPYMuLOtMJ1Tr4pXLHmgjcWhG6RlknY2oNTdQ==
  dependencies:
    "@tanstack/virtual-core" "3.11.2"

"@tanstack/table-core@8.21.2":
  version "8.21.2"
  resolved "https://registry.npmjs.org/@tanstack/table-core/-/table-core-8.21.2.tgz"
  integrity sha512-uvXk/U4cBiFMxt+p9/G7yUWI/UbHYbyghLCjlpWZ3mLeIZiUBSKcUnw9UnKkdRz7Z/N4UBuFLWQdJCjUe7HjvA==

"@tanstack/virtual-core@3.11.2":
  version "3.11.2"
  resolved "https://registry.npmjs.org/@tanstack/virtual-core/-/virtual-core-3.11.2.tgz"
  integrity sha512-vTtpNt7mKCiZ1pwU9hfKPhpdVO2sVzFQsxoVBGtOSHxlrRRzYr8iQ2TlwbAcRYCcEiZ9ECAM8kBzH0v2+VzfKw==

"@ts-morph/common@~0.19.0":
  version "0.19.0"
  resolved "https://registry.npmjs.org/@ts-morph/common/-/common-0.19.0.tgz"
  integrity sha512-Unz/WHmd4pGax91rdIKWi51wnVUW11QttMEPpBiBgIewnc9UQIX7UDLxr5vRlqeByXCwhkF6VabSsI0raWcyAQ==
  dependencies:
    fast-glob "^3.2.12"
    minimatch "^7.4.3"
    mkdirp "^2.1.6"
    path-browserify "^1.0.1"

"@types/d3-color@*", "@types/d3-color@^3.1.3":
  version "3.1.3"
  resolved "https://registry.npmjs.org/@types/d3-color/-/d3-color-3.1.3.tgz"
  integrity sha512-iO90scth9WAbmgv7ogoq57O9YpKmFBbmoEoCHDB2xMBY0+/KVrqAaCDyCE16dUspeOvIxFFRI+0sEtqDqy2b4A==

"@types/d3-delaunay@^6.0.4":
  version "6.0.4"
  resolved "https://registry.npmjs.org/@types/d3-delaunay/-/d3-delaunay-6.0.4.tgz"
  integrity sha512-ZMaSKu4THYCU6sV64Lhg6qjf1orxBthaC161plr5KuPHo3CNm8DTHiLw/5Eq2b6TsNP0W0iJrUOFscY6Q450Hw==

"@types/d3-interpolate@^3.0.4":
  version "3.0.4"
  resolved "https://registry.npmjs.org/@types/d3-interpolate/-/d3-interpolate-3.0.4.tgz"
  integrity sha512-mgLPETlrpVV1YRJIglr4Ez47g7Yxjl1lj7YKsiMCb27VJH9W8NVM6Bb9d8kkpG/uAQS5AmbA48q2IAolKKo1MA==
  dependencies:
    "@types/d3-color" "*"

"@types/d3-path@*":
  version "3.1.1"
  resolved "https://registry.npmjs.org/@types/d3-path/-/d3-path-3.1.1.tgz"
  integrity sha512-VMZBYyQvbGmWyWVea0EHs/BwLgxc+MKi1zLDCONksozI4YJMcTt8ZEuIR4Sb1MMTE8MMW49v0IwI5+b7RmfWlg==

"@types/d3-scale@^4.0.8":
  version "4.0.9"
  resolved "https://registry.npmjs.org/@types/d3-scale/-/d3-scale-4.0.9.tgz"
  integrity sha512-dLmtwB8zkAeO/juAMfnV+sItKjlsw2lKdZVVy6LRr0cBmegxSABiLEpGVmSJJ8O08i4+sGR6qQtb6WtuwJdvVw==
  dependencies:
    "@types/d3-time" "*"

"@types/d3-shape@^3.1.6":
  version "3.1.7"
  resolved "https://registry.npmjs.org/@types/d3-shape/-/d3-shape-3.1.7.tgz"
  integrity sha512-VLvUQ33C+3J+8p+Daf+nYSOsjB4GXp19/S/aGo60m9h1v6XaxjiT82lKVWJCfzhtuZ3yD7i/TPeC/fuKLLOSmg==
  dependencies:
    "@types/d3-path" "*"

"@types/d3-time@*", "@types/d3-time@^3.0.3":
  version "3.0.4"
  resolved "https://registry.npmjs.org/@types/d3-time/-/d3-time-3.0.4.tgz"
  integrity sha512-yuzZug1nkAAaBlBBikKZTgzCeA+k1uy4ZFwWANOfKw5z5LRhV0gNA7gNkKm7HoK+HRN0wX3EkxGk0fpbWhmB7g==

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz"
  integrity sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==

"@types/lodash.debounce@^4.0.7":
  version "4.0.9"
  resolved "https://registry.npmjs.org/@types/lodash.debounce/-/lodash.debounce-4.0.9.tgz"
  integrity sha512-Ma5JcgTREwpLRwMM+XwBR7DaWe96nC38uCBDFKZWbNKD+osjVzdpnUSwBcqCptrp16sSOLBAUb50Car5I0TCsQ==
  dependencies:
    "@types/lodash" "*"

"@types/lodash@*":
  version "4.17.16"
  resolved "https://registry.npmjs.org/@types/lodash/-/lodash-4.17.16.tgz"
  integrity sha512-HX7Em5NYQAXKW+1T+FiuG27NGwzJfCX3s1GjOa7ujxZa52kjJLOr4FUxT+giF6Tgxv1e+/czV/iTtBw27WTU9g==

"@types/md5@^2.3.5":
  version "2.3.5"
  resolved "https://registry.npmjs.org/@types/md5/-/md5-2.3.5.tgz"
  integrity sha512-/i42wjYNgE6wf0j2bcTX6kuowmdL/6PE4IVitMpm2eYKBUuYCprdcWVK+xEF0gcV6ufMCRhtxmReGfc6hIK7Jw==

"@types/node-fetch@^2.6.4":
  version "2.6.12"
  resolved "https://registry.npmjs.org/@types/node-fetch/-/node-fetch-2.6.12.tgz"
  integrity sha512-8nneRWKCg3rMtF69nLQJnOYUcbafYeFSjqkw3jCRLsqkWFlHaoQrr5mXmofFGOx3DKn7UfmBMyov8ySvLRVldA==
  dependencies:
    "@types/node" "*"
    form-data "^4.0.0"

"@types/node@*", "@types/node@^20":
  version "20.17.24"
  resolved "https://registry.npmjs.org/@types/node/-/node-20.17.24.tgz"
  integrity sha512-d7fGCyB96w9BnWQrOsJtpyiSaBcAYYr75bnK6ZRjDbql2cGLj/3GsL5OYmLPNq76l7Gf2q4Rv9J2o6h5CrD9sA==
  dependencies:
    undici-types "~6.19.2"

"@types/node@^18.11.18":
  version "18.19.80"
  resolved "https://registry.npmjs.org/@types/node/-/node-18.19.80.tgz"
  integrity sha512-kEWeMwMeIvxYkeg1gTc01awpwLbfMRZXdIhwRcakd/KlK53jmRC26LqcbIt7fnAQTu5GzlnWmzA3H6+l1u6xxQ==
  dependencies:
    undici-types "~5.26.4"

"@types/parse-json@^4.0.0":
  version "4.0.2"
  resolved "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.2.tgz"
  integrity sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==

"@types/pdf-parse@^1.1.4":
  version "1.1.4"
  resolved "https://registry.npmjs.org/@types/pdf-parse/-/pdf-parse-1.1.4.tgz"
  integrity sha512-+gbBHbNCVGGYw1S9lAIIvrHW47UYOhMIFUsJcMkMrzy1Jf0vulBN3XQIjPgnoOXveMuHnF3b57fXROnY/Or7eg==

"@types/phoenix@^1.5.4":
  version "1.6.6"
  resolved "https://registry.npmjs.org/@types/phoenix/-/phoenix-1.6.6.tgz"
  integrity sha512-PIzZZlEppgrpoT2QgbnDU+MMzuR6BbCjllj0bM70lWoejMeNJAxCchxnv7J3XFkI8MpygtRpzXrIlmWUBclP5A==

"@types/prop-types@*", "@types/prop-types@^15.7.12", "@types/prop-types@^15.7.14":
  version "15.7.14"
  resolved "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.14.tgz"
  integrity sha512-gNMvNH49DJ7OJYv+KAKn0Xp45p8PLl6zo2YnvDIbTd4J6MER2BmWN49TG7n9LvkyihINxeKW8+3bfS2yDC9dzQ==

"@types/react-color@^3.0.12":
  version "3.0.13"
  resolved "https://registry.npmjs.org/@types/react-color/-/react-color-3.0.13.tgz"
  integrity sha512-2c/9FZ4ixC5T3JzN0LP5Cke2Mf0MKOP2Eh0NPDPWmuVH3NjPyhEjqNMQpN1Phr5m74egAy+p2lYNAFrX1z9Yrg==
  dependencies:
    "@types/reactcss" "*"

"@types/react-dom@*", "@types/react-dom@^18":
  version "18.3.5"
  resolved "https://registry.npmjs.org/@types/react-dom/-/react-dom-18.3.5.tgz"
  integrity sha512-P4t6saawp+b/dFrUr2cvkVsfvPguwsxtH6dNIYRllMsefqFzkZk5UIjzyDOv5g1dXIPdG4Sp1yCR4Z6RCUsG/Q==

"@types/react-transition-group@^4.4.10":
  version "4.4.12"
  resolved "https://registry.npmjs.org/@types/react-transition-group/-/react-transition-group-4.4.12.tgz"
  integrity sha512-8TV6R3h2j7a91c+1DXdJi3Syo69zzIZbz7Lg5tORM5LEJG7X/E6a1V3drRyBRZq7/utz7A+c4OgYLiLcYGHG6w==

"@types/react@*", "@types/react@^17.0.0 || ^18.0.0 || ^19.0.0", "@types/react@^18", "@types/react@^18.0.0":
  version "18.3.18"
  resolved "https://registry.npmjs.org/@types/react/-/react-18.3.18.tgz"
  integrity sha512-t4yC+vtgnkYjNSKlFx1jkAhH8LgTo2N/7Qvi83kdEaUtMDiwpbLAktKDaAMlRcJ5eSxZkH74eEGt1ky31d7kfQ==
  dependencies:
    "@types/prop-types" "*"
    csstype "^3.0.2"

"@types/reactcss@*":
  version "1.2.13"
  resolved "https://registry.npmjs.org/@types/reactcss/-/reactcss-1.2.13.tgz"
  integrity sha512-gi3S+aUi6kpkF5vdhUsnkwbiSEIU/BEJyD7kBy2SudWBUuKmJk8AQKE0OVcQQeEy40Azh0lV6uynxlikYIJuwg==

"@types/regenerator-runtime@^0.13.5":
  version "0.13.8"
  resolved "https://registry.npmjs.org/@types/regenerator-runtime/-/regenerator-runtime-0.13.8.tgz"
  integrity sha512-jjKoBekfYDH331060tZhosdJVDnXIXx+T8Iw2h2T4HEds6Ddb2lr0JxD15+XPKlXwRHRNgZoY+4Fb2ykoqzHBg==

"@types/retry@0.12.0":
  version "0.12.0"
  resolved "https://registry.npmjs.org/@types/retry/-/retry-0.12.0.tgz"
  integrity sha512-wWKOClTTiizcZhXnPY4wikVAwmdYHp8q6DmC+EJUzAMsycb7HB32Kh9RN4+0gExjmPmZSAQjgURXIGATPegAvA==

"@types/uuid@^10.0.0":
  version "10.0.0"
  resolved "https://registry.npmjs.org/@types/uuid/-/uuid-10.0.0.tgz"
  integrity sha512-7gqG38EyHgyP1S+7+xomFtL+ZNHcKv6DwNaCZmJmo1vgMugyF3TCnXVg4t1uk89mLNwnLtnY3TpOpCOyp1/xHQ==

"@types/ws@^8.5.10":
  version "8.18.0"
  resolved "https://registry.npmjs.org/@types/ws/-/ws-8.18.0.tgz"
  integrity sha512-8svvI3hMyvN0kKCJMvTJP/x6Y/EoQbepff882wL+Sn5QsXb3etnamgrJq4isrBxSJj5L2AuXcI0+bgkoAXGUJw==
  dependencies:
    "@types/node" "*"

"@typescript-eslint/parser@^5.4.2 || ^6.0.0":
  version "6.21.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-6.21.0.tgz"
  integrity sha512-tbsV1jPne5CkFQCgPBcDOt30ItF7aJoZL997JSF7MhGQqOeT3svWRYxiqlfA5RUdlHN6Fi+EI9bxqbdyAUZjYQ==
  dependencies:
    "@typescript-eslint/scope-manager" "6.21.0"
    "@typescript-eslint/types" "6.21.0"
    "@typescript-eslint/typescript-estree" "6.21.0"
    "@typescript-eslint/visitor-keys" "6.21.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@6.21.0":
  version "6.21.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-6.21.0.tgz"
  integrity sha512-OwLUIWZJry80O99zvqXVEioyniJMa+d2GrqpUTqi5/v5D5rOrppJVBPa0yKCblcigC0/aYAzxxqQ1B+DS2RYsg==
  dependencies:
    "@typescript-eslint/types" "6.21.0"
    "@typescript-eslint/visitor-keys" "6.21.0"

"@typescript-eslint/types@6.21.0":
  version "6.21.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/types/-/types-6.21.0.tgz"
  integrity sha512-1kFmZ1rOm5epu9NZEZm1kckCDGj5UJEf7P1kliH4LKu/RkwpsfqqGmY2OOcUs18lSlQBKLDYBOGxRVtrMN5lpg==

"@typescript-eslint/typescript-estree@6.21.0":
  version "6.21.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-6.21.0.tgz"
  integrity sha512-6npJTkZcO+y2/kr+z0hc4HwNfrrP4kNYh57ek7yCNlrBjWQ1Y0OS7jiZTkgumrvkX5HkEKXFZkkdFNkaW2wmUQ==
  dependencies:
    "@typescript-eslint/types" "6.21.0"
    "@typescript-eslint/visitor-keys" "6.21.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    minimatch "9.0.3"
    semver "^7.5.4"
    ts-api-utils "^1.0.1"

"@typescript-eslint/visitor-keys@6.21.0":
  version "6.21.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-6.21.0.tgz"
  integrity sha512-JJtkDduxLi9bivAB+cYOVMtbkqdPOhZ+ZI5LC47MIRrDV4Yn2o+ZnW10Nkmr28xRpSpdJ6Sm42Hjf2+REYXm0A==
  dependencies:
    "@typescript-eslint/types" "6.21.0"
    eslint-visitor-keys "^3.4.1"

"@ungap/structured-clone@^1.2.0":
  version "1.3.0"
  resolved "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.3.0.tgz"
  integrity sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g==

"@zxcvbn-ts/core@3.0.4":
  version "3.0.4"
  resolved "https://registry.npmjs.org/@zxcvbn-ts/core/-/core-3.0.4.tgz"
  integrity sha512-aQeiT0F09FuJaAqNrxynlAwZ2mW/1MdXakKWNmGM1Qp/VaY6CnB/GfnMS2T8gB2231Esp1/maCWd8vTG4OuShw==
  dependencies:
    fastest-levenshtein "1.0.16"

"@zxcvbn-ts/language-common@3.0.4":
  version "3.0.4"
  resolved "https://registry.npmjs.org/@zxcvbn-ts/language-common/-/language-common-3.0.4.tgz"
  integrity sha512-viSNNnRYtc7ULXzxrQIVUNwHAPSXRtoIwy/Tq4XQQdIknBzw4vz36lQLF6mvhMlTIlpjoN/Z1GFu/fwiAlUSsw==

abort-controller@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz"
  integrity sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==
  dependencies:
    event-target-shim "^5.0.0"

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  integrity sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", acorn@^8.9.0:
  version "8.14.1"
  resolved "https://registry.npmjs.org/acorn/-/acorn-8.14.1.tgz"
  integrity sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg==

agent-base@^7.0.2:
  version "7.1.3"
  resolved "https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz"
  integrity sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==

agentkeepalive@^4.2.1:
  version "4.6.0"
  resolved "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.6.0.tgz"
  integrity sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==
  dependencies:
    humanize-ms "^1.2.1"

ajv@^6.12.4:
  version "6.12.6"
  resolved "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-regex@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz"
  integrity sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==

ansi-styles@^4.0.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz"
  integrity sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==

ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz"
  integrity sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==

any-promise@^1.0.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz"
  integrity sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==

anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

arg@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz"
  integrity sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
  integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==

aria-hidden@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npmjs.org/aria-hidden/-/aria-hidden-1.2.4.tgz"
  integrity sha512-y+CcFFwelSXpLZk/7fMB2mUbGtX9lKycf1MWJ7CaTIERyitVlyQx6C+sxcROU2BAJ24OiZyK+8wj2i8AlBoS3A==
  dependencies:
    tslib "^2.0.0"

aria-query@^5.3.2:
  version "5.3.2"
  resolved "https://registry.npmjs.org/aria-query/-/aria-query-5.3.2.tgz"
  integrity sha512-COROpnaoap1E2F000S62r6A60uHZnmlvomhfyT2DlTcrY1OrBKn2UhH7qn5wTC9zMvD0AY7csdPSNwKP+7WiQw==

array-buffer-byte-length@^1.0.1, array-buffer-byte-length@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.2.tgz"
  integrity sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==
  dependencies:
    call-bound "^1.0.3"
    is-array-buffer "^3.0.5"

array-includes@^3.1.6, array-includes@^3.1.8:
  version "3.1.8"
  resolved "https://registry.npmjs.org/array-includes/-/array-includes-3.1.8.tgz"
  integrity sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.4"
    is-string "^1.0.7"

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz"
  integrity sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==

array.prototype.findlast@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmjs.org/array.prototype.findlast/-/array.prototype.findlast-1.2.5.tgz"
  integrity sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.findlastindex@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmjs.org/array.prototype.findlastindex/-/array.prototype.findlastindex-1.2.5.tgz"
  integrity sha512-zfETvRFA8o7EiNn++N5f/kaCw221hrpGsDmcpndVupkPzEc1Wuf3VgC0qby1BbHs7f5DVYjgtEU2LLh5bqeGfQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.flat@^1.3.1, array.prototype.flat@^1.3.2:
  version "1.3.3"
  resolved "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.3.3.tgz"
  integrity sha512-rwG/ja1neyLqCuGZ5YYrznA62D4mZXg0i1cIskIUKSiqF3Cje9/wXAls9B9s1Wa2fomMsIv8czB8jZcPmxCXFg==
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.flatmap@^1.3.2, array.prototype.flatmap@^1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.3.3.tgz"
  integrity sha512-Y7Wt51eKJSyi80hFrJCePGGNo5ktJCslFuboqJsbf57CCPcm5zztluPlc4/aD8sWsKvlwatezpV4U1efk8kpjg==
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.tosorted@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/array.prototype.tosorted/-/array.prototype.tosorted-1.1.4.tgz"
  integrity sha512-p6Fx8B7b7ZhL/gmUsAy0D15WhvDccw3mnGNbZpi3pmeJdxtWsj2jEaI4Y6oo3XiHfzuSgPwKc04MYt6KgvC/wA==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"
    es-errors "^1.3.0"
    es-shim-unscopables "^1.0.2"

arraybuffer.prototype.slice@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.4.tgz"
  integrity sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    is-array-buffer "^3.0.4"

ast-types-flow@^0.0.8:
  version "0.0.8"
  resolved "https://registry.npmjs.org/ast-types-flow/-/ast-types-flow-0.0.8.tgz"
  integrity sha512-OH/2E5Fg20h2aPrbe+QL8JZQFko0YZaF+j4mnQ7BGhfavO7OpSLa8a0y9sBwomHdSbkhTS8TQNayBfnW5DwbvQ==

ast-types@^0.16.1:
  version "0.16.1"
  resolved "https://registry.npmjs.org/ast-types/-/ast-types-0.16.1.tgz"
  integrity sha512-6t10qk83GOG8p0vKmaCr8eiilZwO171AvbROMtvvNiwrTly62t+7XkA8RdIIVbpMhCASAsxgAzdRSwh6nw/5Dg==
  dependencies:
    tslib "^2.0.1"

async-function@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/async-function/-/async-function-1.0.0.tgz"
  integrity sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA==

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

attr-accept@^2.2.4:
  version "2.2.5"
  resolved "https://registry.npmjs.org/attr-accept/-/attr-accept-2.2.5.tgz"
  integrity sha512-0bDNnY/u6pPwHDMoF0FieU354oBi0a8rD9FcsLwzcGWbc8KS8KPIi7y+s13OlVY+gMWc/9xEMUgNE6Qm8ZllYQ==

autoprefixer@^10.0.1:
  version "10.4.20"
  resolved "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.20.tgz"
  integrity sha512-XY25y5xSv/wEoqzDyXXME4AFfkZI0P23z6Fs3YgymDnKJkCGOnkL0iTxCa85UTqaSgfcqyf3UA6+c7wUvx/16g==
  dependencies:
    browserslist "^4.23.3"
    caniuse-lite "^1.0.30001646"
    fraction.js "^4.3.7"
    normalize-range "^0.1.2"
    picocolors "^1.0.1"
    postcss-value-parser "^4.2.0"

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz"
  integrity sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==
  dependencies:
    possible-typed-array-names "^1.0.0"

axe-core@^4.10.0:
  version "4.10.3"
  resolved "https://registry.npmjs.org/axe-core/-/axe-core-4.10.3.tgz"
  integrity sha512-Xm7bpRXnDSX2YE2YFfBk2FnF0ep6tmG7xPh8iHee8MIcrgq762Nkce856dYtJYLkuIoYZvGfTs/PbZhideTcEg==

axios@*, axios@^1.6.7:
  version "1.8.2"
  resolved "https://registry.npmjs.org/axios/-/axios-1.8.2.tgz"
  integrity sha512-ls4GYBm5aig9vWx8AWDSGLpnpDQRtWAfrjU+EuytuODrFBkqesN2RkOQCBzrA1RQNHw1SmRMSDDDSwzNAYQ6Rg==
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

axobject-query@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/axobject-query/-/axobject-query-4.1.0.tgz"
  integrity sha512-qIj0G9wZbMGNLjLmg1PT6v2mE9AH2zlnADJD/2tC6E00hgmhUOfEB6greHPAfLRSufHqROIUTkw6E+M3lH0PTQ==

babel-plugin-macros@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz"
  integrity sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg==
  dependencies:
    "@babel/runtime" "^7.12.5"
    cosmiconfig "^7.0.0"
    resolve "^1.19.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

base-64@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/base-64/-/base-64-0.1.0.tgz"
  integrity sha512-Y5gU45svrR5tI2Vt/X9GPd3L0HNIKzGu202EjxrXMpuc2V2CiKgemAbUUsqYmZJvPtCXoUKjNZwBJzsNScUbXA==

base64-js@^1.3.1, base64-js@^1.5.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

binary-extensions@^2.0.0, binary-extensions@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz"
  integrity sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==

binary-search@^1.3.5:
  version "1.3.6"
  resolved "https://registry.npmjs.org/binary-search/-/binary-search-1.3.6.tgz"
  integrity sha512-nbE1WxOTTrUWIfsfZ4aHGYu5DOuNkbxGokjV6Z2kxfJK3uaAb8zNK1muzOeipoLHZjInT4Br88BHpzevc681xA==

bl@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/bl/-/bl-5.1.0.tgz"
  integrity sha512-tv1ZJHLfTDnXE6tMHv73YgSJaWR2AFuPwMntBe7XL/GBFHnT0CLnsHMogfk5+GzCDC5ZWarSCYaIGATZt9dNsQ==
  dependencies:
    buffer "^6.0.3"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz"
  integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz"
  integrity sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==
  dependencies:
    fill-range "^7.1.1"

browser-tabs-lock@1.2.15:
  version "1.2.15"
  resolved "https://registry.npmjs.org/browser-tabs-lock/-/browser-tabs-lock-1.2.15.tgz"
  integrity sha512-J8K9vdivK0Di+b8SBdE7EZxDr88TnATing7XoLw6+nFkXMQ6sVBh92K3NQvZlZU91AIkFRi0w3sztk5Z+vsswA==
  dependencies:
    lodash ">=4.17.21"

browserslist@^4.23.3, browserslist@^4.24.0, "browserslist@>= 4.21.0":
  version "4.24.4"
  resolved "https://registry.npmjs.org/browserslist/-/browserslist-4.24.4.tgz"
  integrity sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==
  dependencies:
    caniuse-lite "^1.0.30001688"
    electron-to-chromium "^1.5.73"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.1"

buffer@^6.0.3:
  version "6.0.3"
  resolved "https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz"
  integrity sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.2.1"

busboy@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz"
  integrity sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==
  dependencies:
    streamsearch "^1.1.0"

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz"
  integrity sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.7, call-bind@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz"
  integrity sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2, call-bound@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz"
  integrity sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

camelcase-css@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz"
  integrity sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==

camelcase@6:
  version "6.3.0"
  resolved "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz"
  integrity sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==

caniuse-lite@^1.0.30001579, caniuse-lite@^1.0.30001646, caniuse-lite@^1.0.30001688:
  version "1.0.30001702"
  resolved "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001702.tgz"
  integrity sha512-LoPe/D7zioC0REI5W73PeR1e1MLCipRGq/VkovJnd6Df+QVqT+vT33OXCp8QUd7kA7RZrHWxb1B36OQKI/0gOA==

chalk@^4.0.0:
  version "4.1.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^5.0.0, chalk@5.2.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/chalk/-/chalk-5.2.0.tgz"
  integrity sha512-ree3Gqw/nazQAPuJJEy+avdl7QfZMcUvmHIKgEZkGL+xOBzRvup5Hxo6LHuMceSxOabuJLJm5Yp/92R9eMmMvA==

charenc@0.0.2:
  version "0.0.2"
  resolved "https://registry.npmjs.org/charenc/-/charenc-0.0.2.tgz"
  integrity sha512-yrLQ/yVUFXkzg7EDQsPieE/53+0RlaWTs+wBrvW36cyilJ2SaDWfl4Yj7MtLTXleV9uEKefbAGUPv2/iWSooRA==

chokidar@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz"
  integrity sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

class-variance-authority@^0.7.0:
  version "0.7.1"
  resolved "https://registry.npmjs.org/class-variance-authority/-/class-variance-authority-0.7.1.tgz"
  integrity sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg==
  dependencies:
    clsx "^2.1.1"

cli-cursor@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/cli-cursor/-/cli-cursor-4.0.0.tgz"
  integrity sha512-VGtlMu3x/4DOtIUwEkRezxUZ2lBacNJCHash0N0WeZDBS+7Ux1dm3XWAgWYxLJFMMdOeXMHXorshEFhbMSGelg==
  dependencies:
    restore-cursor "^4.0.0"

cli-spinners@^2.6.1:
  version "2.9.2"
  resolved "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.2.tgz"
  integrity sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==

client-only@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/client-only/-/client-only-0.0.1.tgz"
  integrity sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==

clone@^1.0.2:
  version "1.0.4"
  resolved "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz"
  integrity sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==

clsx@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/clsx/-/clsx-1.2.1.tgz"
  integrity sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==

clsx@^2.0.0, clsx@^2.1.0, clsx@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz"
  integrity sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==

code-block-writer@^12.0.0:
  version "12.0.0"
  resolved "https://registry.npmjs.org/code-block-writer/-/code-block-writer-12.0.0.tgz"
  integrity sha512-q4dMFMlXtKR3XNBHyMHt/3pwYNA69EDk00lloMOaaUMKPUXBw6lpXtbu3MMVG6/uOihGnRDOlkyqsONEUj60+w==

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-string@^1.9.0:
  version "1.9.1"
  resolved "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz"
  integrity sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmjs.org/color/-/color-4.2.3.tgz"
  integrity sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==
  dependencies:
    color-convert "^2.0.1"
    color-string "^1.9.0"

color2k@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmjs.org/color2k/-/color2k-2.0.3.tgz"
  integrity sha512-zW190nQTIoXcGCaU08DvVNFTmQhUpnJfVuAKfWqUQkflXKpaDdpaYoM0iluLS9lgJNHyBF58KKA2FBEwkD7wog==

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

commander@^10.0.0, commander@^10.0.1:
  version "10.0.1"
  resolved "https://registry.npmjs.org/commander/-/commander-10.0.1.tgz"
  integrity sha512-y4Mg2tXshplEbSGzx7amzPwKKOCGuoSRP/CjEdwwk0FOGlUbq6lKuoyDZTNZkmxHdJtp54hdfY/JUrdL7Xfdug==

commander@^4.0.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz"
  integrity sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==

compute-scroll-into-view@^3.0.2:
  version "3.1.1"
  resolved "https://registry.npmjs.org/compute-scroll-into-view/-/compute-scroll-into-view-3.1.1.tgz"
  integrity sha512-VRhuHOLoKYOy4UbilLbUzbYg93XLjv2PncJC50EuTWPA3gaja1UjBsUP/D/9/juV3vQFr6XBEzn9KCAHdUvOHw==

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==

convert-source-map@^1.5.0:
  version "1.9.0"
  resolved "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz"
  integrity sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz"
  integrity sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==

cookie@0.5.0:
  version "0.5.0"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.5.0.tgz"
  integrity sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==

copy-to-clipboard@3.3.3:
  version "3.3.3"
  resolved "https://registry.npmjs.org/copy-to-clipboard/-/copy-to-clipboard-3.3.3.tgz"
  integrity sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==
  dependencies:
    toggle-selection "^1.0.6"

core-js@3.26.1:
  version "3.26.1"
  resolved "https://registry.npmjs.org/core-js/-/core-js-3.26.1.tgz"
  integrity sha512-21491RRQVzUn0GGM9Z1Jrpr6PNPxPi+Za8OM9q4tksTSnlbXXGKK1nXNg/QvwFYettXvSX6zWKCtHHfjN4puyA==

cosmiconfig@^7.0.0:
  version "7.1.0"
  resolved "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.1.0.tgz"
  integrity sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

cosmiconfig@^8.1.3:
  version "8.3.6"
  resolved "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.3.6.tgz"
  integrity sha512-kcZ6+W5QzcJ3P1Mt+83OUv/oHFqZHIx8DuxG6eZ5RGMERoLqp4BuGjhHLYGK+Kf5XVkQvqBSmAy/nGWN3qDgEA==
  dependencies:
    import-fresh "^3.3.0"
    js-yaml "^4.1.0"
    parse-json "^5.2.0"
    path-type "^4.0.0"

cross-spawn@^7.0.2, cross-spawn@^7.0.3, cross-spawn@^7.0.6:
  version "7.0.6"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz"
  integrity sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypt@0.0.2:
  version "0.0.2"
  resolved "https://registry.npmjs.org/crypt/-/crypt-0.0.2.tgz"
  integrity sha512-mCxBlsHFYh9C+HVpiEacem8FEBnMXgU9gy4zmNC+SXAZNB/1idgp/aulFJ4FgCi7GPEVbfyng092GqL2k2rmow==

crypto-js@4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/crypto-js/-/crypto-js-4.2.0.tgz"
  integrity sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/*****************************/Vg==

csstype@^3.0.2, csstype@3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/csstype/-/csstype-3.1.1.tgz"
  integrity sha512-DJR/VvkAvSZW9bTouZue2sSxDwdTN92uHjqeKVm+0dAqdfNykRzQ95tay8aXMBAAPpUiq4Qcug2L7neoRh2Egw==

csstype@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz"
  integrity sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==

"d3-array@2 - 3", "d3-array@2.10.0 - 3":
  version "3.2.4"
  resolved "https://registry.npmjs.org/d3-array/-/d3-array-3.2.4.tgz"
  integrity sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==
  dependencies:
    internmap "1 - 2"

d3-color@^3.1.0, "d3-color@1 - 3":
  version "3.1.0"
  resolved "https://registry.npmjs.org/d3-color/-/d3-color-3.1.0.tgz"
  integrity sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==

d3-delaunay@^6.0.4:
  version "6.0.4"
  resolved "https://registry.npmjs.org/d3-delaunay/-/d3-delaunay-6.0.4.tgz"
  integrity sha512-mdjtIZ1XLAM8bm/hx3WwjfHt6Sggek7qH043O8KEjDXN40xi3vx/6pYSVTwLjEgiXQTbvaouWKynLBiUZ6SK6A==
  dependencies:
    delaunator "5"

"d3-format@1 - 3":
  version "3.1.0"
  resolved "https://registry.npmjs.org/d3-format/-/d3-format-3.1.0.tgz"
  integrity sha512-YyUI6AEuY/Wpt8KWLgZHsIU86atmikuoOmCfommt0LYHiQSPjvX2AcFc38PX0CBpr2RCyZhjex+NS/LPOv6YqA==

d3-interpolate@^3.0.1, "d3-interpolate@1.2.0 - 3":
  version "3.0.1"
  resolved "https://registry.npmjs.org/d3-interpolate/-/d3-interpolate-3.0.1.tgz"
  integrity sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==
  dependencies:
    d3-color "1 - 3"

d3-path@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/d3-path/-/d3-path-3.1.0.tgz"
  integrity sha512-p3KP5HCf/bvjBSSKuXid6Zqijx7wIfNW+J/maPs+iwR35at5JCbLUT0LzF1cnjbCHWhqzQTIN2Jpe8pRebIEFQ==

d3-scale@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/d3-scale/-/d3-scale-4.0.2.tgz"
  integrity sha512-GZW464g1SH7ag3Y7hXjf8RoUuAFIqklOAq3MRl4OaWabTFJY9PN/E1YklhXLh+OQ3fM9yS2nOkCoS+WLZ6kvxQ==
  dependencies:
    d3-array "2.10.0 - 3"
    d3-format "1 - 3"
    d3-interpolate "1.2.0 - 3"
    d3-time "2.1.1 - 3"
    d3-time-format "2 - 4"

d3-shape@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/d3-shape/-/d3-shape-3.2.0.tgz"
  integrity sha512-SaLBuwGm3MOViRq2ABk3eLoxwZELpH6zhl3FbAoJ7Vm1gofKx6El1Ib5z23NUEhF9AsGl7y+dzLe5Cw2AArGTA==
  dependencies:
    d3-path "^3.1.0"

"d3-time-format@2 - 4":
  version "4.1.0"
  resolved "https://registry.npmjs.org/d3-time-format/-/d3-time-format-4.1.0.tgz"
  integrity sha512-dJxPBlzC7NugB2PDLwo9Q8JiTR3M3e4/XANkreKSUxF8vvXKqm1Yfq4Q5dl8budlunRVlUUaDUgFt7eA8D6NLg==
  dependencies:
    d3-time "1 - 3"

d3-time@^3.1.0, "d3-time@1 - 3", "d3-time@2.1.1 - 3":
  version "3.1.0"
  resolved "https://registry.npmjs.org/d3-time/-/d3-time-3.1.0.tgz"
  integrity sha512-VqKjzBLejbSMT4IgbmVgDjpkYrNWUYJnbCGo874u7MMKIWsILRX+OpX/gTk8MqjpT1A/c6HY2dCA77ZN0lkQ2Q==
  dependencies:
    d3-array "2 - 3"

daemon@>=0.3.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/daemon/-/daemon-1.1.0.tgz"
  integrity sha512-1vX9YVcP21gt12nSD3SQRC/uPU7fyA6M8qyClTBIFuiRWoylFn57PwXhjBAqRl085bZAje7sILhZU48qcS9SWw==

damerau-levenshtein@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/damerau-levenshtein/-/damerau-levenshtein-1.0.8.tgz"
  integrity sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==

data-uri-to-buffer@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz"
  integrity sha512-0R9ikRb668HB7QDxT1vkpuUBtqc53YyAwMwGeUFKRojY/NWKvdZ+9UYtRfGmhqNbRkTSVpMbmyhXipFFv2cb/A==

data-view-buffer@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/data-view-buffer/-/data-view-buffer-1.0.2.tgz"
  integrity sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-length@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/data-view-byte-length/-/data-view-byte-length-1.0.2.tgz"
  integrity sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-offset@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/data-view-byte-offset/-/data-view-byte-offset-1.0.1.tgz"
  integrity sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

debug@^3.1.0:
  version "3.2.7"
  resolved "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
  integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
  dependencies:
    ms "^2.1.1"

debug@^3.2.7:
  version "3.2.7"
  resolved "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
  integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
  dependencies:
    ms "^2.1.1"

debug@^4.1.0, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4, debug@^4.3.7, debug@4:
  version "4.4.0"
  resolved "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz"
  integrity sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==
  dependencies:
    ms "^2.1.3"

decamelize@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz"
  integrity sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==

decimal.js@10:
  version "10.5.0"
  resolved "https://registry.npmjs.org/decimal.js/-/decimal.js-10.5.0.tgz"
  integrity sha512-8vDa8Qxvr/+d94hSh5P3IJwI5t8/c0KsMp+g8bNw9cY2icONa5aPfvKeieW1WlG0WQYwwhJ7mjui2xtiePQSXw==

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz"
  integrity sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==

deepmerge@4.3.1:
  version "4.3.1"
  resolved "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz"
  integrity sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==

defaults@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz"
  integrity sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==
  dependencies:
    clone "^1.0.2"

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.3, define-properties@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz"
  integrity sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

delaunator@^5.0.1, delaunator@5:
  version "5.0.1"
  resolved "https://registry.npmjs.org/delaunator/-/delaunator-5.0.1.tgz"
  integrity sha512-8nvh+XBe96aCESrGOqMp/84b13H9cdKbG5P2ejQCh4d4sK9RL4371qou9drQjMhvnPmhWl5hnmqbEE0fXr9Xnw==
  dependencies:
    robust-predicates "^3.0.2"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

dequal@2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz"
  integrity sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==

detect-libc@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.3.tgz"
  integrity sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==

detect-node-es@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/detect-node-es/-/detect-node-es-1.1.0.tgz"
  integrity sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==

didyoumean@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz"
  integrity sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==

diff@^5.1.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/diff/-/diff-5.2.0.tgz"
  integrity sha512-uIFDxqpRZGZ6ThOk84hEfqWoHx2devRFvpTZcTHur85vImfaxUbTW9Ryh4CpCuDnToOP1CEtXKIgytHBPVff5A==

digest-fetch@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/digest-fetch/-/digest-fetch-1.3.0.tgz"
  integrity sha512-CGJuv6iKNM7QyZlM2T3sPAdZWd/p9zQiRNS9G+9COUCwzWFTs0Xp8NF5iePx7wtvhDykReiRRrSeNb4oMmB8lA==
  dependencies:
    base-64 "^0.1.0"
    md5 "^2.3.0"

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz"
  integrity sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==
  dependencies:
    path-type "^4.0.0"

dlv@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz"
  integrity sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz"
  integrity sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz"
  integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
  dependencies:
    esutils "^2.0.2"

dom-helpers@^5.0.1:
  version "5.2.1"
  resolved "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz"
  integrity sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==
  dependencies:
    "@babel/runtime" "^7.8.7"
    csstype "^3.0.2"

dot-case@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmjs.org/dot-case/-/dot-case-3.0.4.tgz"
  integrity sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

dunder-proto@^1.0.0, dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz"
  integrity sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz"
  integrity sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==

electron-to-chromium@^1.5.73:
  version "1.5.113"
  resolved "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.113.tgz"
  integrity sha512-wjT2O4hX+wdWPJ76gWSkMhcHAV2PTMX+QetUCPYEdCIe+cxmgzzSSiGRCKW8nuh4mwKZlpv0xvoW7OF2X+wmHg==

embla-carousel-react@^8.0.0-rc22:
  version "8.5.2"
  resolved "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.5.2.tgz"
  integrity sha512-Tmx+uY3MqseIGdwp0ScyUuxpBgx5jX1f7od4Cm5mDwg/dptEiTKf9xp6tw0lZN2VA9JbnVMl/aikmbc53c6QFA==
  dependencies:
    embla-carousel "8.5.2"
    embla-carousel-reactive-utils "8.5.2"

embla-carousel-reactive-utils@8.5.2:
  version "8.5.2"
  resolved "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.5.2.tgz"
  integrity sha512-QC8/hYSK/pEmqEdU1IO5O+XNc/Ptmmq7uCB44vKplgLKhB/l0+yvYx0+Cv0sF6Ena8Srld5vUErZkT+yTahtDg==

embla-carousel@8.5.2:
  version "8.5.2"
  resolved "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.5.2.tgz"
  integrity sha512-xQ9oVLrun/eCG/7ru3R+I5bJ7shsD8fFwLEY7yPe27/+fDHCNj0OT5EoG5ZbFyOxOcG6yTwW8oTz/dWyFnyGpg==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz"
  integrity sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==

enhanced-resolve@^5.15.0:
  version "5.18.1"
  resolved "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.1.tgz"
  integrity sha512-ZSW3ma5GkcQBIpwZTSRAI8N71Uuwgs93IezB7mf7R60tC8ZbJideoDNKjHn2O9KIlx6rkGTTEk1xUCK2E1Y2Yg==
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.17.5, es-abstract@^1.23.2, es-abstract@^1.23.3, es-abstract@^1.23.5, es-abstract@^1.23.6, es-abstract@^1.23.9:
  version "1.23.9"
  resolved "https://registry.npmjs.org/es-abstract/-/es-abstract-1.23.9.tgz"
  integrity sha512-py07lI0wjxAC/DcfK1S6G7iANonniZwTISvdPzk9hzeH0IZIshbuuFxLIU96OyF89Yb9hiqWn8M/bY83KY5vzA==
  dependencies:
    array-buffer-byte-length "^1.0.2"
    arraybuffer.prototype.slice "^1.0.4"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    data-view-buffer "^1.0.2"
    data-view-byte-length "^1.0.2"
    data-view-byte-offset "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-set-tostringtag "^2.1.0"
    es-to-primitive "^1.3.0"
    function.prototype.name "^1.1.8"
    get-intrinsic "^1.2.7"
    get-proto "^1.0.0"
    get-symbol-description "^1.1.0"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    internal-slot "^1.1.0"
    is-array-buffer "^3.0.5"
    is-callable "^1.2.7"
    is-data-view "^1.0.2"
    is-regex "^1.2.1"
    is-shared-array-buffer "^1.0.4"
    is-string "^1.1.1"
    is-typed-array "^1.1.15"
    is-weakref "^1.1.0"
    math-intrinsics "^1.1.0"
    object-inspect "^1.13.3"
    object-keys "^1.1.1"
    object.assign "^4.1.7"
    own-keys "^1.0.1"
    regexp.prototype.flags "^1.5.3"
    safe-array-concat "^1.1.3"
    safe-push-apply "^1.0.0"
    safe-regex-test "^1.1.0"
    set-proto "^1.0.0"
    string.prototype.trim "^1.2.10"
    string.prototype.trimend "^1.0.9"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.3"
    typed-array-byte-length "^1.0.3"
    typed-array-byte-offset "^1.0.4"
    typed-array-length "^1.0.7"
    unbox-primitive "^1.1.0"
    which-typed-array "^1.1.18"

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz"
  integrity sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-iterator-helpers@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/es-iterator-helpers/-/es-iterator-helpers-1.2.1.tgz"
  integrity sha512-uDn+FE1yrDzyC0pCo961B2IHbdM8y/ACZsKD4dG6WqrjV53BADjwa7D+1aom2rsNVfLyDgU/eigvlJGJ08OQ4w==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-set-tostringtag "^2.0.3"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.6"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    iterator.prototype "^1.1.4"
    safe-array-concat "^1.1.3"

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz"
  integrity sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.0.3, es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz"
  integrity sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

es-shim-unscopables@^1.0.2:
  version "1.1.0"
  resolved "https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.1.0.tgz"
  integrity sha512-d9T8ucsEhh8Bi1woXCf+TIKDIROLG5WCkxg8geBCbvk22kzwC5G2OnXVMO6FUsvQlgUUXQ2itephWDLqDzbeCw==
  dependencies:
    hasown "^2.0.2"

es-to-primitive@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.3.0.tgz"
  integrity sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==
  dependencies:
    is-callable "^1.2.7"
    is-date-object "^1.0.5"
    is-symbol "^1.0.4"

escalade@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz"
  integrity sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==

eslint-config-next@14.0.3:
  version "14.0.3"
  resolved "https://registry.npmjs.org/eslint-config-next/-/eslint-config-next-14.0.3.tgz"
  integrity sha512-IKPhpLdpSUyKofmsXUfrvBC49JMUTdeaD8ZIH4v9Vk0sC1X6URTuTJCLtA0Vwuj7V/CQh0oISuSTvNn5//Buew==
  dependencies:
    "@next/eslint-plugin-next" "14.0.3"
    "@rushstack/eslint-patch" "^1.3.3"
    "@typescript-eslint/parser" "^5.4.2 || ^6.0.0"
    eslint-import-resolver-node "^0.3.6"
    eslint-import-resolver-typescript "^3.5.2"
    eslint-plugin-import "^2.28.1"
    eslint-plugin-jsx-a11y "^6.7.1"
    eslint-plugin-react "^7.33.2"
    eslint-plugin-react-hooks "^4.5.0 || 5.0.0-canary-7118f5dd7-20230705"

eslint-config-prettier@^10.0.1:
  version "10.1.1"
  resolved "https://registry.npmjs.org/eslint-config-prettier/-/eslint-config-prettier-10.1.1.tgz"
  integrity sha512-4EQQr6wXwS+ZJSzaR5ZCrYgLxqvUjdXctaEtBqHcbkW944B1NQyO4qpdHQbXBONfwxXdkAY81HH4+LUfrg+zPw==

eslint-import-resolver-node@^0.3.6, eslint-import-resolver-node@^0.3.9:
  version "0.3.9"
  resolved "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.9.tgz"
  integrity sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.13.0"
    resolve "^1.22.4"

eslint-import-resolver-typescript@^3.5.2:
  version "3.8.3"
  resolved "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.8.3.tgz"
  integrity sha512-A0bu4Ks2QqDWNpeEgTQMPTngaMhuDu4yv6xpftBMAf+1ziXnpx+eSR1WRfoPTe2BAiAjHFZ7kSNx1fvr5g5pmQ==
  dependencies:
    "@nolyfill/is-core-module" "1.0.39"
    debug "^4.3.7"
    enhanced-resolve "^5.15.0"
    get-tsconfig "^4.10.0"
    is-bun-module "^1.0.2"
    stable-hash "^0.0.4"
    tinyglobby "^0.2.12"

eslint-module-utils@^2.12.0:
  version "2.12.0"
  resolved "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.12.0.tgz"
  integrity sha512-wALZ0HFoytlyh/1+4wuZ9FJCD/leWHQzzrxJ8+rebyReSLk7LApMyd3WJaLVoN+D5+WIdJyDK1c6JnE65V4Zyg==
  dependencies:
    debug "^3.2.7"

eslint-plugin-import@*, eslint-plugin-import@^2.28.1:
  version "2.31.0"
  resolved "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.31.0.tgz"
  integrity sha512-ixmkI62Rbc2/w8Vfxyh1jQRTdRTF52VxwRVHl/ykPAmqG+Nb7/kNn+byLP0LxPgI7zWA16Jt82SybJInmMia3A==
  dependencies:
    "@rtsao/scc" "^1.1.0"
    array-includes "^3.1.8"
    array.prototype.findlastindex "^1.2.5"
    array.prototype.flat "^1.3.2"
    array.prototype.flatmap "^1.3.2"
    debug "^3.2.7"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.9"
    eslint-module-utils "^2.12.0"
    hasown "^2.0.2"
    is-core-module "^2.15.1"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.fromentries "^2.0.8"
    object.groupby "^1.0.3"
    object.values "^1.2.0"
    semver "^6.3.1"
    string.prototype.trimend "^1.0.8"
    tsconfig-paths "^3.15.0"

eslint-plugin-jsx-a11y@^6.7.1:
  version "6.10.2"
  resolved "https://registry.npmjs.org/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.10.2.tgz"
  integrity sha512-scB3nz4WmG75pV8+3eRUQOHZlNSUhFNq37xnpgRkCCELU3XMvXAxLk1eqWWyE22Ki4Q01Fnsw9BA3cJHDPgn2Q==
  dependencies:
    aria-query "^5.3.2"
    array-includes "^3.1.8"
    array.prototype.flatmap "^1.3.2"
    ast-types-flow "^0.0.8"
    axe-core "^4.10.0"
    axobject-query "^4.1.0"
    damerau-levenshtein "^1.0.8"
    emoji-regex "^9.2.2"
    hasown "^2.0.2"
    jsx-ast-utils "^3.3.5"
    language-tags "^1.0.9"
    minimatch "^3.1.2"
    object.fromentries "^2.0.8"
    safe-regex-test "^1.0.3"
    string.prototype.includes "^2.0.1"

"eslint-plugin-react-hooks@^4.5.0 || 5.0.0-canary-7118f5dd7-20230705":
  version "5.0.0-canary-7118f5dd7-20230705"
  resolved "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-5.0.0-canary-7118f5dd7-20230705.tgz"
  integrity sha512-AZYbMo/NW9chdL7vk6HQzQhT+PvTAEVqWk9ziruUoW2kAOcN5qNyelv70e0F1VNQAbvutOC9oc+xfWycI9FxDw==

eslint-plugin-react@^7.33.2:
  version "7.37.4"
  resolved "https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.37.4.tgz"
  integrity sha512-BGP0jRmfYyvOyvMoRX/uoUeW+GqNj9y16bPQzqAHf3AYII/tDs+jMN0dBVkl88/OZwNGwrVFxE7riHsXVfy/LQ==
  dependencies:
    array-includes "^3.1.8"
    array.prototype.findlast "^1.2.5"
    array.prototype.flatmap "^1.3.3"
    array.prototype.tosorted "^1.1.4"
    doctrine "^2.1.0"
    es-iterator-helpers "^1.2.1"
    estraverse "^5.3.0"
    hasown "^2.0.2"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.8"
    object.fromentries "^2.0.8"
    object.values "^1.2.1"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.5"
    semver "^6.3.1"
    string.prototype.matchall "^4.0.12"
    string.prototype.repeat "^1.0.0"

eslint-scope@^7.2.2:
  version "7.2.2"
  resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz"
  integrity sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  integrity sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==

eslint@*, "eslint@^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9", "eslint@^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9", "eslint@^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7", "eslint@^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0", "eslint@^6.0.0 || ^7.0.0 || >=8.0.0", "eslint@^7.0.0 || ^8.0.0", "eslint@^7.23.0 || ^8.0.0", eslint@^8, eslint@>=7.0.0:
  version "8.57.1"
  resolved "https://registry.npmjs.org/eslint/-/eslint-8.57.1.tgz"
  integrity sha512-ypowyDxpVSYpkXr9WPv2PAZCtNip1Mv5KTW0SCurXv/9iOpcrH9PaqUElksqEB6pChqHGDRCFTyrZlGhnLNGiA==
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.57.1"
    "@humanwhocodes/config-array" "^0.13.0"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.2"
    eslint-visitor-keys "^3.4.3"
    espree "^9.6.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

espree@^9.6.0, espree@^9.6.1:
  version "9.6.1"
  resolved "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz"
  integrity sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esprima@~4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==

esquery@^1.4.2:
  version "1.6.0"
  resolved "https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz"
  integrity sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
  integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
  dependencies:
    estraverse "^5.2.0"

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==

event-target-shim@^5.0.0:
  version "5.0.1"
  resolved "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz"
  integrity sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==

eventemitter3@^4.0.4:
  version "4.0.7"
  resolved "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz"
  integrity sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==

eventemitter3@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/eventemitter3/-/eventemitter3-5.0.1.tgz"
  integrity sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==

events@^3.3.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
  integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==

execa@^7.0.0:
  version "7.2.0"
  resolved "https://registry.npmjs.org/execa/-/execa-7.2.0.tgz"
  integrity sha512-UduyVP7TLB5IcAQl+OzLyLcS/l32W/GLg+AhHJ+ow40FOk2U3SAllPwR44v4vmdFwIWqpdwxxpQbF1n5ta9seA==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.1"
    human-signals "^4.3.0"
    is-stream "^3.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^5.1.0"
    onetime "^6.0.0"
    signal-exit "^3.0.7"
    strip-final-newline "^3.0.0"

expr-eval@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/expr-eval/-/expr-eval-2.0.2.tgz"
  integrity sha512-4EMSHGOPSwAfBiibw3ndnP0AvjDWLsMvGOvWEZ2F96IGk0bIVdjQisOHxReSkE13mHcfbuCiXw+G4y0zv6N8Eg==

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-glob@^3.2.12, fast-glob@^3.2.9, fast-glob@^3.3.2:
  version "3.3.3"
  resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz"
  integrity sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  integrity sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==

fastest-levenshtein@1.0.16:
  version "1.0.16"
  resolved "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.16.tgz"
  integrity sha512-eRnCtTTtGZFpQCwhJiUOuxPQWRXVKYDn0b2PeHfXL6/Zi53SLAzAHfVhVWK2AryC/WH05kGfxhFIPvTF0SXQzg==

fastq@^1.6.0:
  version "1.19.1"
  resolved "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz"
  integrity sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==
  dependencies:
    reusify "^1.0.4"

fdir@^6.4.3:
  version "6.4.3"
  resolved "https://registry.npmjs.org/fdir/-/fdir-6.4.3.tgz"
  integrity sha512-PMXmW2y1hDDfTSRc9gaXIuCCRpuoz3Kaz8cUelp3smouvfT632ozg2vrT6lJsHKKOF59YLbOGfAWGUcKEfRMQw==

fetch-blob@^3.1.2, fetch-blob@^3.1.4:
  version "3.2.0"
  resolved "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz"
  integrity sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ==
  dependencies:
    node-domexception "^1.0.0"
    web-streams-polyfill "^3.0.3"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  integrity sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==
  dependencies:
    flat-cache "^3.0.4"

file-selector@^2.1.0:
  version "2.1.2"
  resolved "https://registry.npmjs.org/file-selector/-/file-selector-2.1.2.tgz"
  integrity sha512-QgXo+mXTe8ljeqUFaX3QVHc5osSItJ/Km+xpocx0aSqWGMSCf6qYs/VnzZgS864Pjn5iceMRFigeAV7AfTlaig==
  dependencies:
    tslib "^2.7.0"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz"
  integrity sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==
  dependencies:
    to-regex-range "^5.0.1"

find-root@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/find-root/-/find-root-1.1.0.tgz"
  integrity sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng==

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz"
  integrity sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "https://registry.npmjs.org/flat-cache/-/flat-cache-3.2.0.tgz"
  integrity sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flat@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmjs.org/flat/-/flat-5.0.2.tgz"
  integrity sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==

flatted@^3.2.9:
  version "3.3.3"
  resolved "https://registry.npmjs.org/flatted/-/flatted-3.3.3.tgz"
  integrity sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==

follow-redirects@^1.15.6:
  version "1.15.9"
  resolved "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz"
  integrity sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==

for-each@^0.3.3:
  version "0.3.5"
  resolved "https://registry.npmjs.org/for-each/-/for-each-0.3.5.tgz"
  integrity sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg==
  dependencies:
    is-callable "^1.2.7"

foreground-child@^3.1.0:
  version "3.3.1"
  resolved "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz"
  integrity sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==
  dependencies:
    cross-spawn "^7.0.6"
    signal-exit "^4.0.1"

form-data-encoder@1.7.2:
  version "1.7.2"
  resolved "https://registry.npmjs.org/form-data-encoder/-/form-data-encoder-1.7.2.tgz"
  integrity sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==

form-data@^4.0.0:
  version "4.0.2"
  resolved "https://registry.npmjs.org/form-data/-/form-data-4.0.2.tgz"
  integrity sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    mime-types "^2.1.12"

formdata-node@^4.3.2:
  version "4.4.1"
  resolved "https://registry.npmjs.org/formdata-node/-/formdata-node-4.4.1.tgz"
  integrity sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==
  dependencies:
    node-domexception "1.0.0"
    web-streams-polyfill "4.0.0-beta.3"

formdata-polyfill@^4.0.10:
  version "4.0.10"
  resolved "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz"
  integrity sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g==
  dependencies:
    fetch-blob "^3.1.2"

fraction.js@^4.3.7:
  version "4.3.7"
  resolved "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz"
  integrity sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==

framer-motion@^11.3.21, "framer-motion@>=11.5.6 || >=12.0.0-alpha.1":
  version "11.18.2"
  resolved "https://registry.npmjs.org/framer-motion/-/framer-motion-11.18.2.tgz"
  integrity sha512-5F5Och7wrvtLVElIpclDT0CBzMVg3dL22B64aZwHtsIY8RB4mXICLrkajK4G9R+ieSAGcgrLeae2SeUTg2pr6w==
  dependencies:
    motion-dom "^11.18.1"
    motion-utils "^11.18.1"
    tslib "^2.4.0"

fs-extra@^11.1.0:
  version "11.3.0"
  resolved "https://registry.npmjs.org/fs-extra/-/fs-extra-11.3.0.tgz"
  integrity sha512-Z4XaCL6dUDHfP/jT25jJKMmtxvuwbkrD1vNSMFlo9lNLY2c5FHYSQgHPRZUjAB26TpDEoW9HCOgplrdbaPV/ew==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==

fs@^0.0.1-security:
  version "0.0.1-security"
  resolved "https://registry.npmjs.org/fs/-/fs-0.0.1-security.tgz"
  integrity sha512-3XY9e1pP0CVEUCdj5BmfIZxRBTSDycnbqhIOGec9QYtmVH2fbLpj86CFWkrNOkt/Fvty4KZG5lTglL9j/gJ87w==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

function.prototype.name@^1.1.6, function.prototype.name@^1.1.8:
  version "1.1.8"
  resolved "https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.8.tgz"
  integrity sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    functions-have-names "^1.2.3"
    hasown "^2.0.2"
    is-callable "^1.2.7"

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz"
  integrity sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
  integrity sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==

get-intrinsic@^1.2.4, get-intrinsic@^1.2.5, get-intrinsic@^1.2.6, get-intrinsic@^1.2.7, get-intrinsic@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz"
  integrity sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-nonce@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/get-nonce/-/get-nonce-1.0.1.tgz"
  integrity sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==

get-proto@^1.0.0, get-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz"
  integrity sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-stream@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz"
  integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==

get-symbol-description@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.1.0.tgz"
  integrity sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"

get-tsconfig@^4.10.0:
  version "4.10.0"
  resolved "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.10.0.tgz"
  integrity sha512-kGzZ3LWWQcGIAmg6iWvXn0ei6WDtV26wzHRMwDSzmAbcXrTEXxHy6IehI6/4eT6VRKyMP1eF1VqwrVUmE/LR7A==
  dependencies:
    resolve-pkg-maps "^1.0.0"

glob-parent@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"
  integrity sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==

glob@^10.3.10:
  version "10.4.5"
  resolved "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz"
  integrity sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

glob@^7.1.3, glob@7.1.7:
  version "7.1.7"
  resolved "https://registry.npmjs.org/glob/-/glob-7.1.7.tgz"
  integrity sha512-OvD9ENzPLbegENnYP5UUfJIirTg4+XwMWGaQfQTY0JenxNvvIKP3U3/tAQSPIu/lHxXYSZmpXlUHeqAIdKzBLQ==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==

globals@^13.19.0:
  version "13.24.0"
  resolved "https://registry.npmjs.org/globals/-/globals-13.24.0.tgz"
  integrity sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/globalthis/-/globalthis-1.0.4.tgz"
  integrity sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==
  dependencies:
    define-properties "^1.2.1"
    gopd "^1.0.1"

globby@^11.1.0:
  version "11.1.0"
  resolved "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz"
  integrity sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz"
  integrity sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==

graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.11, graceful-fs@^4.2.4:
  version "4.2.11"
  resolved "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

graphemer@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz"
  integrity sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==

has-bigints@^1.0.2:
  version "1.1.0"
  resolved "https://registry.npmjs.org/has-bigints/-/has-bigints-1.1.0.tgz"
  integrity sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg==

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/has-proto/-/has-proto-1.2.0.tgz"
  integrity sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==
  dependencies:
    dunder-proto "^1.0.0"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz"
  integrity sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==

has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
  integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

hoist-non-react-statics@^3.3.1:
  version "3.3.2"
  resolved "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
  integrity sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==
  dependencies:
    react-is "^16.7.0"

https-proxy-agent@^6.2.0:
  version "6.2.1"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-6.2.1.tgz"
  integrity sha512-ONsE3+yfZF2caH5+bJlcddtWqNI3Gvs5A38+ngvljxaBiRXRswym2c7yf8UAeFpRFKjFNHIFEHqR/OLAWJzyiA==
  dependencies:
    agent-base "^7.0.2"
    debug "4"

human-signals@^4.3.0:
  version "4.3.1"
  resolved "https://registry.npmjs.org/human-signals/-/human-signals-4.3.1.tgz"
  integrity sha512-nZXjEF2nbo7lIw3mgYjItAfgQXog3OjJogSbKa2CQIIvSGWcKgeJnQlNXip6NglNzYH45nSRiEVimMvYL8DDqQ==

humanize-ms@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/humanize-ms/-/humanize-ms-1.2.1.tgz"
  integrity sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==
  dependencies:
    ms "^2.0.0"

ieee754@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz"
  integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==

ignore@^5.2.0:
  version "5.3.2"
  resolved "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz"
  integrity sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==

import-fresh@^3.2.1, import-fresh@^3.3.0:
  version "3.3.1"
  resolved "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz"
  integrity sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.3, inherits@^2.0.4, inherits@2:
  version "2.0.4"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

init@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/init/-/init-0.1.2.tgz"
  integrity sha512-IvHUjULS2q+BXJdiu4FHkByh3+qSFmkOXQ2ItSfYTtkdUksQc0yNX6f1uDyokzRV71tjpFsFc3ckeYLJXunTGw==
  dependencies:
    daemon ">=0.3.0"

input-otp@1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/input-otp/-/input-otp-1.4.1.tgz"
  integrity sha512-+yvpmKYKHi9jIGngxagY9oWiiblPB7+nEO75F2l2o4vs+6vpPZZmUl4tBNYuTCvQjhvEIbdNeJu70bhfYP2nbw==

internal-slot@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/internal-slot/-/internal-slot-1.1.0.tgz"
  integrity sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.2"
    side-channel "^1.1.0"

"internmap@1 - 2":
  version "2.0.3"
  resolved "https://registry.npmjs.org/internmap/-/internmap-2.0.3.tgz"
  integrity sha512-5Hh7Y1wQbvY5ooGgPbDaL5iYLAPzMTUrjMulskHLH6wnv/A+1q5rgEaiuqEjB+oxGXIVZs1FF+R/KPN3ZSQYYg==

intl-messageformat@^10.1.0:
  version "10.7.15"
  resolved "https://registry.npmjs.org/intl-messageformat/-/intl-messageformat-10.7.15.tgz"
  integrity sha512-LRyExsEsefQSBjU2p47oAheoKz+EOJxSLDdjOaEjdriajfHsMXOmV/EhMvYSg9bAgCUHasuAC+mcUBe/95PfIg==
  dependencies:
    "@formatjs/ecma402-abstract" "2.3.3"
    "@formatjs/fast-memoize" "2.2.6"
    "@formatjs/icu-messageformat-parser" "2.11.1"
    tslib "2"

is-any-array@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-any-array/-/is-any-array-2.0.1.tgz"
  integrity sha512-UtilS7hLRu++wb/WBAw9bNuP1Eg04Ivn1vERJck8zJthEvXCBEBpGR/33u/xLKWEQf95803oalHrVDptcAvFdQ==

is-array-buffer@^3.0.4, is-array-buffer@^3.0.5:
  version "3.0.5"
  resolved "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.5.tgz"
  integrity sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz"
  integrity sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==

is-async-function@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-async-function/-/is-async-function-2.1.1.tgz"
  integrity sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==
  dependencies:
    async-function "^1.0.0"
    call-bound "^1.0.3"
    get-proto "^1.0.1"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-bigint@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-bigint/-/is-bigint-1.1.0.tgz"
  integrity sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==
  dependencies:
    has-bigints "^1.0.2"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.2.1:
  version "1.2.2"
  resolved "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.2.2.tgz"
  integrity sha512-wa56o2/ElJMYqjCjGkXri7it5FbebW5usLw/nPmCMs5DeZ7eziSYZhSmPRn0txqeW4LnAmQQU7FgqLpsEFKM4A==
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-buffer@~1.1.6:
  version "1.1.6"
  resolved "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz"
  integrity sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==

is-bun-module@^1.0.2:
  version "1.3.0"
  resolved "https://registry.npmjs.org/is-bun-module/-/is-bun-module-1.3.0.tgz"
  integrity sha512-DgXeu5UWI0IsMQundYb5UAOzm6G2eVnarJ0byP6Tm55iZNKceD59LNPA2L4VvsScTtHcw0yEkVwSf7PC+QoLSA==
  dependencies:
    semver "^7.6.3"

is-callable@^1.2.7:
  version "1.2.7"
  resolved "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz"
  integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==

is-core-module@^2.13.0, is-core-module@^2.15.1, is-core-module@^2.16.0:
  version "2.16.1"
  resolved "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz"
  integrity sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==
  dependencies:
    hasown "^2.0.2"

is-data-view@^1.0.1, is-data-view@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/is-data-view/-/is-data-view-1.0.2.tgz"
  integrity sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==
  dependencies:
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    is-typed-array "^1.1.13"

is-date-object@^1.0.5, is-date-object@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-date-object/-/is-date-object-1.1.0.tgz"
  integrity sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-finalizationregistry@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/is-finalizationregistry/-/is-finalizationregistry-1.1.1.tgz"
  integrity sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==
  dependencies:
    call-bound "^1.0.3"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-generator-function@^1.0.10:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.1.0.tgz"
  integrity sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==
  dependencies:
    call-bound "^1.0.3"
    get-proto "^1.0.0"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-interactive@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/is-interactive/-/is-interactive-2.0.0.tgz"
  integrity sha512-qP1vozQRI+BMOPcjFzrjXuQvdak2pHNUMZoeG2eRbiSqyvbEf/wQtEOTOX1guk6E3t36RkaqiSt8A/6YElNxLQ==

is-map@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/is-map/-/is-map-2.0.3.tgz"
  integrity sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==

is-number-object@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/is-number-object/-/is-number-object-1.1.1.tgz"
  integrity sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-path-inside@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz"
  integrity sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==

is-regex@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/is-regex/-/is-regex-1.2.1.tgz"
  integrity sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-set@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/is-set/-/is-set-2.0.3.tgz"
  integrity sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==

is-shared-array-buffer@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.4.tgz"
  integrity sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==
  dependencies:
    call-bound "^1.0.3"

is-stream@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-stream/-/is-stream-3.0.0.tgz"
  integrity sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==

is-string@^1.0.7, is-string@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/is-string/-/is-string-1.1.1.tgz"
  integrity sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-symbol@^1.0.4, is-symbol@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/is-symbol/-/is-symbol-1.1.1.tgz"
  integrity sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==
  dependencies:
    call-bound "^1.0.2"
    has-symbols "^1.1.0"
    safe-regex-test "^1.1.0"

is-typed-array@^1.1.13, is-typed-array@^1.1.14, is-typed-array@^1.1.15:
  version "1.1.15"
  resolved "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.15.tgz"
  integrity sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==
  dependencies:
    which-typed-array "^1.1.16"

is-unicode-supported@^1.1.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-1.3.0.tgz"
  integrity sha512-43r2mRvz+8JRIKnWJ+3j8JtjRKZ6GmjzfaE/qiBJnikNnYv/6bagRJ1kUhNk8R5EX/GkobD+r+sfxCPJsiKBLQ==

is-weakmap@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/is-weakmap/-/is-weakmap-2.0.2.tgz"
  integrity sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==

is-weakref@^1.0.2, is-weakref@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/is-weakref/-/is-weakref-1.1.1.tgz"
  integrity sha512-6i9mGWSlqzNMEqpCp93KwRS1uUOodk2OJ6b+sq7ZPDSy2WuI5NFIxp/254TytR8ftefexkWn5xNiHUNpPOfSew==
  dependencies:
    call-bound "^1.0.3"

is-weakset@^2.0.3:
  version "2.0.4"
  resolved "https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.4.tgz"
  integrity sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==
  dependencies:
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

isarray@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz"
  integrity sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

iterator.prototype@^1.1.4:
  version "1.1.5"
  resolved "https://registry.npmjs.org/iterator.prototype/-/iterator.prototype-1.1.5.tgz"
  integrity sha512-H0dkQoCa3b2VEeKQBOxFph+JAbcrQdE7KC0UkqwpLmv2EC4P41QXP+rqo9wYodACiG5/WM5s9oDApTU8utwj9g==
  dependencies:
    define-data-property "^1.1.4"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    get-proto "^1.0.0"
    has-symbols "^1.1.0"
    set-function-name "^2.0.2"

jackspeak@^3.1.2:
  version "3.4.3"
  resolved "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz"
  integrity sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jiti@^1.21.6:
  version "1.21.7"
  resolved "https://registry.npmjs.org/jiti/-/jiti-1.21.7.tgz"
  integrity sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==

jose@^4.14.4:
  version "4.15.9"
  resolved "https://registry.npmjs.org/jose/-/jose-4.15.9.tgz"
  integrity sha512-1vUQX+IdDMVPj4k8kOxgUqlcK518yluMuGZwqlr44FS1ppZB/5GWh4rZG89erpOBOJjU/OBsnCVFfapsRz6nEA==

js-cookie@3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/js-cookie/-/js-cookie-3.0.1.tgz"
  integrity sha512-+0rgsUXZu4ncpPxRL+lNEptWMOWl9etvPHc/koSRp6MPwpRYAhmk0dUG00J4bxVV3r9uUzfo24wW0knS07SKSw==

js-cookie@3.0.5:
  version "3.0.5"
  resolved "https://registry.npmjs.org/js-cookie/-/js-cookie-3.0.5.tgz"
  integrity sha512-cEiJEAEoIbWfCZYKWhVwFuvPX1gETRYPw6LlaTKoxD3s2AkXzkCjnp6h0V77ozyqj0jakteJ4YqDJT830+lVGw==

js-tiktoken@^1.0.12, js-tiktoken@^1.0.7:
  version "1.0.19"
  resolved "https://registry.npmjs.org/js-tiktoken/-/js-tiktoken-1.0.19.tgz"
  integrity sha512-XC63YQeEcS47Y53gg950xiZ4IWmkfMe4p2V9OSaBt26q+p47WHn18izuXzSclCI73B7yGqtfRsT6jcZQI0y08g==
  dependencies:
    base64-js "^1.5.1"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
  integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
  dependencies:
    argparse "^2.0.1"

jsesc@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz"
  integrity sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==

json-buffer@3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz"
  integrity sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==

json5@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz"
  integrity sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==
  dependencies:
    minimist "^1.2.0"

json5@^2.2.2, json5@^2.2.3:
  version "2.2.3"
  resolved "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz"
  integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz"
  integrity sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonpointer@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/jsonpointer/-/jsonpointer-5.0.1.tgz"
  integrity sha512-p/nXbhSEcu3pZRdkW1OfJhpsVtW1gd4Wa1fnQc9YLiTfAjn0312eMKimbdIQzuZl9aa9xUGaRlP9T/CJE/ditQ==

"jsx-ast-utils@^2.4.1 || ^3.0.0", jsx-ast-utils@^3.3.5:
  version "3.3.5"
  resolved "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz"
  integrity sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    object.assign "^4.1.4"
    object.values "^1.1.6"

keyv@^4.5.3:
  version "4.5.4"
  resolved "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz"
  integrity sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==
  dependencies:
    json-buffer "3.0.1"

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz"
  integrity sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==

langchain@^0.1.4:
  version "0.1.37"
  resolved "https://registry.npmjs.org/langchain/-/langchain-0.1.37.tgz"
  integrity sha512-rpaLEJtRrLYhAViEp7/aHfSkxbgSqHJ5n10tXv3o4kHP/wOin85RpTgewwvGjEaKc3797jOg+sLSk6a7e0UlMg==
  dependencies:
    "@anthropic-ai/sdk" "^0.9.1"
    "@langchain/community" "~0.0.47"
    "@langchain/core" "~0.1.60"
    "@langchain/openai" "~0.0.28"
    "@langchain/textsplitters" "~0.0.0"
    binary-extensions "^2.2.0"
    js-tiktoken "^1.0.7"
    js-yaml "^4.1.0"
    jsonpointer "^5.0.1"
    langchainhub "~0.0.8"
    langsmith "~0.1.7"
    ml-distance "^4.0.0"
    openapi-types "^12.1.3"
    p-retry "4"
    uuid "^9.0.0"
    yaml "^2.2.1"
    zod "^3.22.4"
    zod-to-json-schema "^3.22.3"

langchainhub@~0.0.8:
  version "0.0.11"
  resolved "https://registry.npmjs.org/langchainhub/-/langchainhub-0.0.11.tgz"
  integrity sha512-WnKI4g9kU2bHQP136orXr2bcRdgz9iiTBpTN0jWt9IlScUKnJBoD0aa2HOzHURQKeQDnt2JwqVmQ6Depf5uDLQ==

langsmith@^0.1.56-rc.1, langsmith@~0.1.1, langsmith@~0.1.7:
  version "0.1.68"
  resolved "https://registry.npmjs.org/langsmith/-/langsmith-0.1.68.tgz"
  integrity sha512-otmiysWtVAqzMx3CJ4PrtUBhWRG5Co8Z4o7hSZENPjlit9/j3/vm3TSvbaxpDYakZxtMjhkcJTqrdYFipISEiQ==
  dependencies:
    "@types/uuid" "^10.0.0"
    commander "^10.0.1"
    p-queue "^6.6.2"
    p-retry "4"
    semver "^7.6.3"
    uuid "^10.0.0"

language-subtag-registry@^0.3.20:
  version "0.3.23"
  resolved "https://registry.npmjs.org/language-subtag-registry/-/language-subtag-registry-0.3.23.tgz"
  integrity sha512-0K65Lea881pHotoGEa5gDlMxt3pctLi2RplBb7Ezh4rRdLEOtgi7n4EwK9lamnUCkKBqaeKRVebTq6BAxSkpXQ==

language-tags@^1.0.9:
  version "1.0.9"
  resolved "https://registry.npmjs.org/language-tags/-/language-tags-1.0.9.tgz"
  integrity sha512-MbjN408fEndfiQXbFQ1vnd+1NoLDsnQW41410oQBXiyXDMYH5z505juWa4KUE1LqxRC7DgOgZDbKLxHIwm27hA==
  dependencies:
    language-subtag-registry "^0.3.20"

levn@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz"
  integrity sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lilconfig@^3.0.0, lilconfig@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.3.tgz"
  integrity sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==

livekit-client@^2.5.1:
  version "2.9.5"
  resolved "https://registry.npmjs.org/livekit-client/-/livekit-client-2.9.5.tgz"
  integrity sha512-2EJmiB4XItaRjTEmL4XxGzsahLYTer9T5N6lKyhBHQxwH4GrjBWewPySvJEO8zCpD2nvWZCmCQjIJx0+w+y6DA==
  dependencies:
    "@livekit/mutex" "1.1.1"
    "@livekit/protocol" "1.33.0"
    events "^3.3.0"
    loglevel "^1.9.2"
    sdp-transform "^2.15.0"
    ts-debounce "^4.0.0"
    tslib "2.8.1"
    typed-emitter "^2.1.0"
    webrtc-adapter "^9.0.1"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz"
  integrity sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==
  dependencies:
    p-locate "^5.0.0"

lodash-es@^4.17.15:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz"
  integrity sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==

lodash._reinterpolate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/lodash._reinterpolate/-/lodash._reinterpolate-3.0.0.tgz"
  integrity sha512-xYHt68QRoYGjeeM/XOE1uJtvXQAgvszfBhjV4yvsQH0u2i9I6cI6c6/eG4Hh3UAOVn0y/xAXwmTzEay49Q//HA==

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
  integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==

lodash.template@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/lodash.template/-/lodash.template-4.5.0.tgz"
  integrity sha512-84vYFxIkmidUiFxidA/KjjH9pAycqW+h980j7Fuz5qxRtO9pgB7MDFTdys1N7A5mcucRiDyEq4fusljItR1T/A==
  dependencies:
    lodash._reinterpolate "^3.0.0"
    lodash.templatesettings "^4.0.0"

lodash.templatesettings@^4.0.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/lodash.templatesettings/-/lodash.templatesettings-4.2.0.tgz"
  integrity sha512-stgLz+i3Aa9mZgnjr/O+v9ruKZsPsndy7qPZOchbqk2cnTU1ZaldKK+v7m54WoKIyxiuMZTKT2H81F8BeAc3ZQ==
  dependencies:
    lodash._reinterpolate "^3.0.0"

lodash@^4.0.1, lodash@^4.17.15, lodash@^4.17.21, lodash@>=4.17.21:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

log-symbols@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/log-symbols/-/log-symbols-5.1.0.tgz"
  integrity sha512-l0x2DvrW294C9uDCoQe1VSU4gf529FkSZ6leBl4TiqZH/e+0R7hSfHQBNut2mNygDgHwvYHfFLn6Oxb3VWj2rA==
  dependencies:
    chalk "^5.0.0"
    is-unicode-supported "^1.1.0"

loglevel@^1.9.2:
  version "1.9.2"
  resolved "https://registry.npmjs.org/loglevel/-/loglevel-1.9.2.tgz"
  integrity sha512-HgMmCqIJSAKqo68l0rS2AanEWfkxaZ5wNiEFb5ggm08lDs9Xl2KxBlX3PTcaD2chBM1gXAYf491/M2Rv8Jwayg==

loose-envify@^1.1.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lower-case@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/lower-case/-/lower-case-2.0.2.tgz"
  integrity sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==
  dependencies:
    tslib "^2.0.3"

lru-cache@^10.2.0:
  version "10.4.3"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz"
  integrity sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz"
  integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
  dependencies:
    yallist "^3.0.2"

lucide-react@^0.294.0:
  version "0.294.0"
  resolved "https://registry.npmjs.org/lucide-react/-/lucide-react-0.294.0.tgz"
  integrity sha512-V7o0/VECSGbLHn3/1O67FUgBwWB+hmzshrgDVRJQhMh8uj5D3HBuIvhuAmQTtlupILSplwIZg5FTc4tTKMA2SA==

map-obj@^4.1.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/map-obj/-/map-obj-4.3.0.tgz"
  integrity sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ==

marked@^13.0.3:
  version "13.0.3"
  resolved "https://registry.npmjs.org/marked/-/marked-13.0.3.tgz"
  integrity sha512-rqRix3/TWzE9rIoFGIn8JmsVfhiuC8VIQ8IdX5TfzmeBucdY05/0UlzKaw0eVtpcN/OdVFpBk7CjKGo9iHJ/zA==

material-colors@^1.2.1:
  version "1.2.6"
  resolved "https://registry.npmjs.org/material-colors/-/material-colors-1.2.6.tgz"
  integrity sha512-6qE4B9deFBIa9YSpOc9O0Sgc43zTeVYbgDT5veRKSlB2+ZuHNoVVxA1L/ckMUayV9Ay9y7Z/SZCLcGteW9i7bg==

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz"
  integrity sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==

md5@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/md5/-/md5-2.3.0.tgz"
  integrity sha512-T1GITYmFaKuO91vxyoQMFETst+O71VUPEU3ze5GNzDm0OWdP8v1ziTaAEPUr/3kLsY3Sftgz242A1SetQiDL7g==
  dependencies:
    charenc "0.0.2"
    crypt "0.0.2"
    is-buffer "~1.1.6"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

micromatch@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz"
  integrity sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12:
  version "2.1.35"
  resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==

mimic-fn@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/mimic-fn/-/mimic-fn-4.0.0.tgz"
  integrity sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==

minimatch@^3.0.4, minimatch@^3.0.5, minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^7.4.3:
  version "7.4.6"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-7.4.6.tgz"
  integrity sha512-sBz8G/YjVniEz6lKPNpKxXwazJe4c19fEfV2GDMX6AjFz+MX9uDWIZW8XreVhkFW3fkIdTv/gxWr/Kks5FFAVw==
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^9.0.4:
  version "9.0.5"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz"
  integrity sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==
  dependencies:
    brace-expansion "^2.0.1"

minimatch@9.0.3:
  version "9.0.3"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-9.0.3.tgz"
  integrity sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.0, minimist@^1.2.6:
  version "1.2.8"
  resolved "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz"
  integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.1.2:
  version "7.1.2"
  resolved "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz"
  integrity sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==

mkdirp@^2.1.6:
  version "2.1.6"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-2.1.6.tgz"
  integrity sha512-+hEnITedc8LAtIP9u3HJDFIdcLV2vXP33sqLLIzkv1Db1zO/1OxbvYf0Y1OC/S/Qo5dxHXepofhmxL02PsKe+A==

ml-array-mean@^1.1.6:
  version "1.1.6"
  resolved "https://registry.npmjs.org/ml-array-mean/-/ml-array-mean-1.1.6.tgz"
  integrity sha512-MIdf7Zc8HznwIisyiJGRH9tRigg3Yf4FldW8DxKxpCCv/g5CafTw0RRu51nojVEOXuCQC7DRVVu5c7XXO/5joQ==
  dependencies:
    ml-array-sum "^1.1.6"

ml-array-sum@^1.1.6:
  version "1.1.6"
  resolved "https://registry.npmjs.org/ml-array-sum/-/ml-array-sum-1.1.6.tgz"
  integrity sha512-29mAh2GwH7ZmiRnup4UyibQZB9+ZLyMShvt4cH4eTK+cL2oEMIZFnSyB3SS8MlsTh6q/w/yh48KmqLxmovN4Dw==
  dependencies:
    is-any-array "^2.0.0"

ml-distance-euclidean@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/ml-distance-euclidean/-/ml-distance-euclidean-2.0.0.tgz"
  integrity sha512-yC9/2o8QF0A3m/0IXqCTXCzz2pNEzvmcE/9HFKOZGnTjatvBbsn4lWYJkxENkA4Ug2fnYl7PXQxnPi21sgMy/Q==

ml-distance@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/ml-distance/-/ml-distance-4.0.1.tgz"
  integrity sha512-feZ5ziXs01zhyFUUUeZV5hwc0f5JW0Sh0ckU1koZe/wdVkJdGxcP06KNQuF0WBTj8FttQUzcvQcpcrOp/XrlEw==
  dependencies:
    ml-array-mean "^1.1.6"
    ml-distance-euclidean "^2.0.0"
    ml-tree-similarity "^1.0.0"

ml-tree-similarity@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/ml-tree-similarity/-/ml-tree-similarity-1.0.0.tgz"
  integrity sha512-XJUyYqjSuUQkNQHMscr6tcjldsOoAekxADTplt40QKfwW6nd++1wHWV9AArl0Zvw/TIHgNaZZNvr8QGvE8wLRg==
  dependencies:
    binary-search "^1.3.5"
    num-sort "^2.0.0"

motion-dom@^11.18.1:
  version "11.18.1"
  resolved "https://registry.npmjs.org/motion-dom/-/motion-dom-11.18.1.tgz"
  integrity sha512-g76KvA001z+atjfxczdRtw/RXOM3OMSdd1f4DL77qCTF/+avrRJiawSG4yDibEQ215sr9kpinSlX2pCTJ9zbhw==
  dependencies:
    motion-utils "^11.18.1"

motion-utils@^11.18.1:
  version "11.18.1"
  resolved "https://registry.npmjs.org/motion-utils/-/motion-utils-11.18.1.tgz"
  integrity sha512-49Kt+HKjtbJKLtgO/LKj9Ld+6vw9BjH5d9sc40R/kVyH8GLAXgT42M2NnuPcJNuA3s9ZfZBUcwIgpmZWGEE+hA==

ms@^2.0.0, ms@^2.1.1, ms@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

mustache@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/mustache/-/mustache-4.2.0.tgz"
  integrity sha512-71ippSywq5Yb7/tVYyGbkBggbU8H3u5Rz56fH60jGFgr8uHwxs+aSKeqmluIVzM0m0kB7xQjKS6qPfd0b2ZoqQ==

mz@^2.7.0:
  version "2.7.0"
  resolved "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz"
  integrity sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nanoid@^3.3.6:
  version "3.3.9"
  resolved "https://registry.npmjs.org/nanoid/-/nanoid-3.3.9.tgz"
  integrity sha512-SppoicMGpZvbF1l3z4x7No3OlIjP7QJvC9XR7AhZr1kL133KHnKPztkKDc+Ir4aJ/1VhTySrtKhrsycmrMQfvg==

nanoid@^3.3.8:
  version "3.3.9"
  resolved "https://registry.npmjs.org/nanoid/-/nanoid-3.3.9.tgz"
  integrity sha512-SppoicMGpZvbF1l3z4x7No3OlIjP7QJvC9XR7AhZr1kL133KHnKPztkKDc+Ir4aJ/1VhTySrtKhrsycmrMQfvg==

nanoid@^5.0.4:
  version "5.1.3"
  resolved "https://registry.npmjs.org/nanoid/-/nanoid-5.1.3.tgz"
  integrity sha512-zAbEOEr7u2CbxwoMRlz/pNSpRP0FdAU4pRaYunCdEezWohXFs+a0Xw7RfkKaezMsmSM1vttcLthJtwRnVtOfHQ==

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  integrity sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==

next-themes@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/next-themes/-/next-themes-0.2.1.tgz"
  integrity sha512-B+AKNfYNIzh0vqQQKqQItTS8evEouKD7H5Hj3kmuPERwddR2TxvDSFZuTj6T7Jfn1oyeUyJMydPl1Bkxkh0W7A==

next@*, "next@^13.5.4 || ^14.0.3 || >=15.0.0-rc", next@^14.2.4:
  version "14.2.24"
  resolved "https://registry.npmjs.org/next/-/next-14.2.24.tgz"
  integrity sha512-En8VEexSJ0Py2FfVnRRh8gtERwDRaJGNvsvad47ShkC2Yi8AXQPXEA2vKoDJlGFSj5WE5SyF21zNi4M5gyi+SQ==
  dependencies:
    "@next/env" "14.2.24"
    "@swc/helpers" "0.5.5"
    busboy "1.6.0"
    caniuse-lite "^1.0.30001579"
    graceful-fs "^4.2.11"
    postcss "8.4.31"
    styled-jsx "5.1.1"
  optionalDependencies:
    "@next/swc-darwin-arm64" "14.2.24"
    "@next/swc-darwin-x64" "14.2.24"
    "@next/swc-linux-arm64-gnu" "14.2.24"
    "@next/swc-linux-arm64-musl" "14.2.24"
    "@next/swc-linux-x64-gnu" "14.2.24"
    "@next/swc-linux-x64-musl" "14.2.24"
    "@next/swc-win32-arm64-msvc" "14.2.24"
    "@next/swc-win32-ia32-msvc" "14.2.24"
    "@next/swc-win32-x64-msvc" "14.2.24"

no-case@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmjs.org/no-case/-/no-case-3.0.4.tgz"
  integrity sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-domexception@^1.0.0, node-domexception@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz"
  integrity sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==

node-ensure@^0.0.0:
  version "0.0.0"
  resolved "https://registry.npmjs.org/node-ensure/-/node-ensure-0.0.0.tgz"
  integrity sha512-DRI60hzo2oKN1ma0ckc6nQWlHU69RH6xN0sjQTjMpChPfTYvKZdcQFfdYK2RWbJcKyUizSIy/l8OTGxMAM1QDw==

node-fetch@^2.6.7:
  version "2.7.0"
  resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz"
  integrity sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==
  dependencies:
    whatwg-url "^5.0.0"

node-fetch@^3.3.0:
  version "3.3.2"
  resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz"
  integrity sha512-dRB78srN/l6gqWulah9SrxeYnxeddIG30+GOqK/9OlLVyLg3HPnr6SqOWTWOXKRwC2eGYCkZ59NNuSgvSrpgOA==
  dependencies:
    data-uri-to-buffer "^4.0.0"
    fetch-blob "^3.1.4"
    formdata-polyfill "^4.0.10"

node-releases@^2.0.19:
  version "2.0.19"
  resolved "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz"
  integrity sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
  integrity sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==

npm-run-path@^5.1.0:
  version "5.3.0"
  resolved "https://registry.npmjs.org/npm-run-path/-/npm-run-path-5.3.0.tgz"
  integrity sha512-ppwTtiJZq0O/ai0z7yfudtBpWIoxM8yE6nHi1X47eFR2EWORqfbu6CnPlNsjeN683eT0qG6H/Pyf9fCcvjnnnQ==
  dependencies:
    path-key "^4.0.0"

num-sort@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/num-sort/-/num-sort-2.1.0.tgz"
  integrity sha512-1MQz1Ed8z2yckoBeSfkQHHO9K1yDRxxtotKSJ9yvcTUUxSvfvzEq5GwBrjjHEpMlq/k5gvXdmJ1SbYxWtpNoVg==

object-assign@^4.0.1, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==

object-hash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz"
  integrity sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==

object-inspect@^1.13.3:
  version "1.13.4"
  resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz"
  integrity sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
  integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==

object.assign@^4.1.4, object.assign@^4.1.7:
  version "4.1.7"
  resolved "https://registry.npmjs.org/object.assign/-/object.assign-4.1.7.tgz"
  integrity sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"
    has-symbols "^1.1.0"
    object-keys "^1.1.1"

object.entries@^1.1.8:
  version "1.1.8"
  resolved "https://registry.npmjs.org/object.entries/-/object.entries-1.1.8.tgz"
  integrity sha512-cmopxi8VwRIAw/fkijJohSfpef5PdN0pMQJN6VC/ZKvn0LIknWD8KtgY6KlQdEc4tIjcQ3HxSMmnvtzIscdaYQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

object.fromentries@^2.0.8:
  version "2.0.8"
  resolved "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.8.tgz"
  integrity sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"

object.groupby@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/object.groupby/-/object.groupby-1.0.3.tgz"
  integrity sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"

object.values@^1.1.6, object.values@^1.2.0, object.values@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/object.values/-/object.values-1.2.1.tgz"
  integrity sha512-gXah6aZrcUxjWg2zR2MwouP2eHlCBzdV4pygudehaKXSGW4v2AsRQUK+lwwXhii6KFZcunEnmSUoYp5CXibxtA==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

once@^1.3.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

onetime@^5.1.0:
  version "5.1.2"
  resolved "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz"
  integrity sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==
  dependencies:
    mimic-fn "^2.1.0"

onetime@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/onetime/-/onetime-6.0.0.tgz"
  integrity sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==
  dependencies:
    mimic-fn "^4.0.0"

openai@*, openai@^4.41.1, openai@^4.6.0:
  version "4.86.2"
  resolved "https://registry.npmjs.org/openai/-/openai-4.86.2.tgz"
  integrity sha512-nvYeFjmjdSu6/msld+22JoUlCICNk/lUFpSMmc6KNhpeNLpqL70TqbD/8Vura/tFmYqHKW0trcjgPwUpKSPwaA==
  dependencies:
    "@types/node" "^18.11.18"
    "@types/node-fetch" "^2.6.4"
    abort-controller "^3.0.0"
    agentkeepalive "^4.2.1"
    form-data-encoder "1.7.2"
    formdata-node "^4.3.2"
    node-fetch "^2.6.7"

openapi-types@^12.1.3:
  version "12.1.3"
  resolved "https://registry.npmjs.org/openapi-types/-/openapi-types-12.1.3.tgz"
  integrity sha512-N4YtSYJqghVu4iek2ZUvcN/0aqH1kRDuNqzcycDxhOUpg7GdvLa2F3DgS6yBNhInhv2r/6I0Flkn7CqL8+nIcw==

optionator@^0.9.3:
  version "0.9.4"
  resolved "https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz"
  integrity sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

ora@^6.1.2:
  version "6.3.1"
  resolved "https://registry.npmjs.org/ora/-/ora-6.3.1.tgz"
  integrity sha512-ERAyNnZOfqM+Ao3RAvIXkYh5joP220yf59gVe2X/cI6SiCxIdi4c9HZKZD8R6q/RDXEje1THBju6iExiSsgJaQ==
  dependencies:
    chalk "^5.0.0"
    cli-cursor "^4.0.0"
    cli-spinners "^2.6.1"
    is-interactive "^2.0.0"
    is-unicode-supported "^1.1.0"
    log-symbols "^5.1.0"
    stdin-discarder "^0.1.0"
    strip-ansi "^7.0.1"
    wcwidth "^1.0.1"

own-keys@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/own-keys/-/own-keys-1.0.1.tgz"
  integrity sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==
  dependencies:
    get-intrinsic "^1.2.6"
    object-keys "^1.1.1"
    safe-push-apply "^1.0.0"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/p-finally/-/p-finally-1.0.0.tgz"
  integrity sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==

p-limit@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz"
  integrity sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz"
  integrity sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==
  dependencies:
    p-limit "^3.0.2"

p-queue@^6.6.2:
  version "6.6.2"
  resolved "https://registry.npmjs.org/p-queue/-/p-queue-6.6.2.tgz"
  integrity sha512-RwFpb72c/BhQLEXIZ5K2e+AhgNVmIejGlTgiB9MzZ0e93GRvqZ7uSi0dvRF7/XIXDeNkra2fNHBxTyPDGySpjQ==
  dependencies:
    eventemitter3 "^4.0.4"
    p-timeout "^3.2.0"

p-retry@4:
  version "4.6.2"
  resolved "https://registry.npmjs.org/p-retry/-/p-retry-4.6.2.tgz"
  integrity sha512-312Id396EbJdvRONlngUx0NydfrIQ5lsYu0znKVUzVvArzEIt08V1qhtyESbGVd1FGX7UKtiFp5uwKZdM8wIuQ==
  dependencies:
    "@types/retry" "0.12.0"
    retry "^0.13.1"

p-timeout@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/p-timeout/-/p-timeout-3.2.0.tgz"
  integrity sha512-rhIwUycgwwKcP9yTOOFK/AKsAopjjCakVqLHePO3CC6Mir1Z99xT+R63jZxAT5lFZLa2inS5h+ZS2GvR99/FBg==
  dependencies:
    p-finally "^1.0.0"

package-json-from-dist@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz"
  integrity sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

parse-json@^5.0.0, parse-json@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz"
  integrity sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

path-browserify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/path-browserify/-/path-browserify-1.0.1.tgz"
  integrity sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g==

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==

path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-key@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-key/-/path-key-4.0.0.tgz"
  integrity sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-scurry@^1.11.1:
  version "1.11.1"
  resolved "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz"
  integrity sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-to-regexp@6.2.2:
  version "6.2.2"
  resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-6.2.2.tgz"
  integrity sha512-GQX3SSMokngb36+whdpRXE+3f9V8UzyAorlYvOGx87ufGHehNTn5lCxrKtLyZ4Yl/wEKnNnr98ZzOwwDZV5ogw==

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
  integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==

pdf-parse@^1.1.1, pdf-parse@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/pdf-parse/-/pdf-parse-1.1.1.tgz"
  integrity sha512-v6ZJ/efsBpGrGGknjtq9J/oC8tZWq0KWL5vQrk2GlzLEQPUDB1ex+13Rmidl1neNN358Jn9EHZw5y07FFtaC7A==
  dependencies:
    debug "^3.1.0"
    node-ensure "^0.0.0"

pdfjs-dist@^4.10.38:
  version "4.10.38"
  resolved "https://registry.npmjs.org/pdfjs-dist/-/pdfjs-dist-4.10.38.tgz"
  integrity sha512-/Y3fcFrXEAsMjJXeL9J8+ZG9U01LbuWaYypvDW2ycW1jL269L3js3DVBjDJ0Up9Np1uqDXsDrRihHANhZOlwdQ==
  optionalDependencies:
    "@napi-rs/canvas" "^0.1.65"

picocolors@^1.0.0, picocolors@^1.0.1, picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz"
  integrity sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

"picomatch@^3 || ^4", picomatch@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz"
  integrity sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==

pify@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz"
  integrity sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==

pirates@^4.0.1:
  version "4.0.6"
  resolved "https://registry.npmjs.org/pirates/-/pirates-4.0.6.tgz"
  integrity sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==

possible-typed-array-names@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz"
  integrity sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg==

postcss-import@^15.1.0:
  version "15.1.0"
  resolved "https://registry.npmjs.org/postcss-import/-/postcss-import-15.1.0.tgz"
  integrity sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==
  dependencies:
    postcss-value-parser "^4.0.0"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-js@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.1.tgz"
  integrity sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==
  dependencies:
    camelcase-css "^2.0.1"

postcss-load-config@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.2.tgz"
  integrity sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==
  dependencies:
    lilconfig "^3.0.0"
    yaml "^2.3.4"

postcss-nested@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.2.0.tgz"
  integrity sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==
  dependencies:
    postcss-selector-parser "^6.1.1"

postcss-selector-parser@^6.1.1, postcss-selector-parser@^6.1.2:
  version "6.1.2"
  resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz"
  integrity sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.0.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss@^8, postcss@^8.0.0, postcss@^8.1.0, postcss@^8.2.14, postcss@^8.4.21, postcss@^8.4.47, postcss@>=8.0.9:
  version "8.5.3"
  resolved "https://registry.npmjs.org/postcss/-/postcss-8.5.3.tgz"
  integrity sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==
  dependencies:
    nanoid "^3.3.8"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

postcss@8.4.31:
  version "8.4.31"
  resolved "https://registry.npmjs.org/postcss/-/postcss-8.4.31.tgz"
  integrity sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==
  dependencies:
    nanoid "^3.3.6"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz"
  integrity sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==

prisma@*, prisma@^5.15.0:
  version "5.22.0"
  resolved "https://registry.npmjs.org/prisma/-/prisma-5.22.0.tgz"
  integrity sha512-vtpjW3XuYCSnMsNVBjLMNkTj6OZbudcPPTPYHqX0CJfpcdWciI1dM8uHETwmDxxiqEwCIE6WvXucWUetJgfu/A==
  dependencies:
    "@prisma/engines" "5.22.0"
  optionalDependencies:
    fsevents "2.3.3"

prompts@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz"
  integrity sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

prop-types@^15.5.10, prop-types@^15.6.2, prop-types@^15.7.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz"
  integrity sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

punycode@^2.1.0:
  version "2.3.1"
  resolved "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz"
  integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==

qrcode.react@3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/qrcode.react/-/qrcode.react-3.1.0.tgz"
  integrity sha512-oyF+Urr3oAMUG/OiOuONL3HXM+53wvuH3mtIWQrYmsXoAq0DkvZp2RYUWFSMFtbdOpuS++9v+WAkzNVkMlNW6Q==

qs@6.11.0:
  version "6.11.0"
  resolved "https://registry.npmjs.org/qs/-/qs-6.11.0.tgz"
  integrity sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==
  dependencies:
    side-channel "^1.0.4"

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

react-audio-player@^0.17.0:
  version "0.17.0"
  resolved "https://registry.npmjs.org/react-audio-player/-/react-audio-player-0.17.0.tgz"
  integrity sha512-aCZgusPxA9HK7rLZcTdhTbBH9l6do9vn3NorgoDZRxRxJlOy9uZWzPaKjd7QdcuP2vXpxGA/61JMnnOEY7NXeA==
  dependencies:
    prop-types "^15.7.2"

react-color@^2.19.3:
  version "2.19.3"
  resolved "https://registry.npmjs.org/react-color/-/react-color-2.19.3.tgz"
  integrity sha512-LEeGE/ZzNLIsFWa1TMe8y5VYqr7bibneWmvJwm1pCn/eNmrabWDh659JSPn9BuaMpEfU83WTOJfnCcjDZwNQTA==
  dependencies:
    "@icons/material" "^0.2.4"
    lodash "^4.17.15"
    lodash-es "^4.17.15"
    material-colors "^1.2.1"
    prop-types "^15.5.10"
    reactcss "^1.2.0"
    tinycolor2 "^1.4.1"

react-dom@*, "react-dom@^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom@^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc", "react-dom@^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom@^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1", "react-dom@^17.0.0 || ^18.0.0 || ^19.0.0", react-dom@^18, "react-dom@^18.0.0 || ^19.0.0", "react-dom@^18.0.0 || ^19.0.0 || ^19.0.0-rc", react-dom@^18.2.0, react-dom@>=16, react-dom@>=16.6.0, react-dom@>=16.8, react-dom@>=16.8.0, react-dom@>=18, "react-dom@>=18 || >=19.0.0-beta", "react-dom@>=18 || >=19.0.0-rc.0":
  version "18.3.1"
  resolved "https://registry.npmjs.org/react-dom/-/react-dom-18.3.1.tgz"
  integrity sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.23.2"

react-dropzone@^14.2.3:
  version "14.3.8"
  resolved "https://registry.npmjs.org/react-dropzone/-/react-dropzone-14.3.8.tgz"
  integrity sha512-sBgODnq+lcA4P296DY4wacOZz3JFpD99fp+hb//iBO2HHnyeZU3FwWyXJ6salNpqQdsZrgMrotuko/BdJMV8Ug==
  dependencies:
    attr-accept "^2.2.4"
    file-selector "^2.1.0"
    prop-types "^15.8.1"

react-hook-form@^7.0.0, react-hook-form@^7.49.0:
  version "7.54.2"
  resolved "https://registry.npmjs.org/react-hook-form/-/react-hook-form-7.54.2.tgz"
  integrity sha512-eHpAUgUjWbZocoQYUHposymRb4ZP6d0uwUnooL2uOybA9/3tPUvoAKqEWK1WaSiTxxOfTpffNZP7QwlnM3/gEg==

react-is@^16.13.1:
  version "16.13.1"
  resolved "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  integrity sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==

react-is@^16.7.0:
  version "16.13.1"
  resolved "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  integrity sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==

react-is@^19.0.0:
  version "19.0.0"
  resolved "https://registry.npmjs.org/react-is/-/react-is-19.0.0.tgz"
  integrity sha512-H91OHcwjZsbq3ClIDHMzBShc1rotbfACdWENsmEf0IFvZ3FgGPtdHMcsv45bQ1hAbgdfiA8SnxTKfDS+x/8m2g==

react-remove-scroll-bar@^2.3.7:
  version "2.3.8"
  resolved "https://registry.npmjs.org/react-remove-scroll-bar/-/react-remove-scroll-bar-2.3.8.tgz"
  integrity sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==
  dependencies:
    react-style-singleton "^2.2.2"
    tslib "^2.0.0"

react-remove-scroll@^2.6.3:
  version "2.6.3"
  resolved "https://registry.npmjs.org/react-remove-scroll/-/react-remove-scroll-2.6.3.tgz"
  integrity sha512-pnAi91oOk8g8ABQKGF5/M9qxmmOPxaAnopyTHYfqYEwJhyFrbbBtHuSgtKEoH0jpcxx5o3hXqH1mNd9/Oi+8iQ==
  dependencies:
    react-remove-scroll-bar "^2.3.7"
    react-style-singleton "^2.2.3"
    tslib "^2.1.0"
    use-callback-ref "^1.3.3"
    use-sidecar "^1.1.3"

react-style-singleton@^2.2.2, react-style-singleton@^2.2.3:
  version "2.2.3"
  resolved "https://registry.npmjs.org/react-style-singleton/-/react-style-singleton-2.2.3.tgz"
  integrity sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==
  dependencies:
    get-nonce "^1.0.0"
    tslib "^2.0.0"

react-textarea-autosize@^8.5.3:
  version "8.5.7"
  resolved "https://registry.npmjs.org/react-textarea-autosize/-/react-textarea-autosize-8.5.7.tgz"
  integrity sha512-2MqJ3p0Jh69yt9ktFIaZmORHXw4c4bxSIhCeWiFwmJ9EYKgLmuNII3e9c9b2UO+ijl4StnpZdqpxNIhTdHvqtQ==
  dependencies:
    "@babel/runtime" "^7.20.13"
    use-composed-ref "^1.3.0"
    use-latest "^1.2.1"

react-transition-group@^4.4.5:
  version "4.4.5"
  resolved "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.5.tgz"
  integrity sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==
  dependencies:
    "@babel/runtime" "^7.5.5"
    dom-helpers "^5.0.1"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"

react@*, "react@^16.11.0 || ^17.0.0 || ^18.0.0", "react@^16.5.1 || ^17.0.0 || ^18.0.0", "react@^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react@^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc", "react@^16.8.0 || ^17 || ^18 || ^19", "react@^16.8.0 || ^17.0.0 || ^18.0.0", "react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react@^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1", "react@^16.8.0 || ^17.0.1 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react@^16.x || ^17.x || ^18.x || ^19.0.0 || ^19.0.0-rc", "react@^17.0.0 || ^18.0.0 || ^19.0.0", react@^18, "react@^18 || ^19", "react@^18.0.0 || ^19.0.0", "react@^18.0.0 || ^19.0.0 || ^19.0.0-rc", react@^18.2.0, react@^18.3.1, "react@>= 16.8 || 18.0.0", "react@>= 16.8.0 || 17.x.x || ^18.0.0-0", react@>=16, react@>=16.6.0, react@>=16.8, react@>=16.8.0, react@>=18, "react@>=18 || >=19.0.0-beta", "react@>=18 || >=19.0.0-rc.0":
  version "18.3.1"
  resolved "https://registry.npmjs.org/react/-/react-18.3.1.tgz"
  integrity sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==
  dependencies:
    loose-envify "^1.1.0"

reactcss@^1.2.0:
  version "1.2.3"
  resolved "https://registry.npmjs.org/reactcss/-/reactcss-1.2.3.tgz"
  integrity sha512-KiwVUcFu1RErkI97ywr8nvx8dNOpT03rbnma0SSalTYjkrPYaEajR4a/MRt6DZ46K6arDRbWMNHF+xH7G7n/8A==
  dependencies:
    lodash "^4.0.1"

read-cache@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz"
  integrity sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==
  dependencies:
    pify "^2.3.0"

readable-stream@^3.4.0:
  version "3.6.2"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz"
  integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

recast@^0.23.2:
  version "0.23.11"
  resolved "https://registry.npmjs.org/recast/-/recast-0.23.11.tgz"
  integrity sha512-YTUo+Flmw4ZXiWfQKGcwwc11KnoRAYgzAE2E7mXKCjSviTKShtxBsN6YUUBB2gtaBzKzeKunxhUwNHQuRryhWA==
  dependencies:
    ast-types "^0.16.1"
    esprima "~4.0.0"
    source-map "~0.6.1"
    tiny-invariant "^1.3.3"
    tslib "^2.0.1"

reflect.getprototypeof@^1.0.6, reflect.getprototypeof@^1.0.9:
  version "1.0.10"
  resolved "https://registry.npmjs.org/reflect.getprototypeof/-/reflect.getprototypeof-1.0.10.tgz"
  integrity sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.9"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.7"
    get-proto "^1.0.1"
    which-builtin-type "^1.2.1"

regenerator-runtime@^0.14.0:
  version "0.14.1"
  resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz"
  integrity sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==

regenerator-runtime@0.13.11:
  version "0.13.11"
  resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz"
  integrity sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==

regexp.prototype.flags@^1.5.3:
  version "1.5.4"
  resolved "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz"
  integrity sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    set-function-name "^2.0.2"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve-pkg-maps@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/resolve-pkg-maps/-/resolve-pkg-maps-1.0.0.tgz"
  integrity sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==

resolve@^1.1.7, resolve@^1.19.0, resolve@^1.22.4, resolve@^1.22.8:
  version "1.22.10"
  resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz"
  integrity sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.5:
  version "2.0.0-next.5"
  resolved "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.5.tgz"
  integrity sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/restore-cursor/-/restore-cursor-4.0.0.tgz"
  integrity sha512-I9fPXU9geO9bHOt9pHHOhOkYerIMsmVaWB0rA2AI9ERh/+x/i7MV5HKBNrg+ljO5eoPVgCcnFuRjJ9uH6I/3eg==
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

retell-client-js-sdk@^2.0.0:
  version "2.0.6"
  resolved "https://registry.npmjs.org/retell-client-js-sdk/-/retell-client-js-sdk-2.0.6.tgz"
  integrity sha512-uFYY/C1sbMUb+4YlH9PWyrLrV+7Dl7qdAzt+m4TNND3ObZdDTLV/Z+JCJc6MAuAbpSmJ9q6lRWQj+w9iqNlwVg==
  dependencies:
    eventemitter3 "^5.0.1"
    livekit-client "^2.5.1"

retell-sdk@^4.19.0:
  version "4.20.0"
  resolved "https://registry.npmjs.org/retell-sdk/-/retell-sdk-4.20.0.tgz"
  integrity sha512-GpFDKGlJ66rM3U2pg/0fWpD7JdXzrSbSM11s8ki9NMzNDgdRJssU/dKHMXkpinw96nEo/+qWd8a6Jz2W85wfYw==
  dependencies:
    "@types/node" "^18.11.18"
    "@types/node-fetch" "^2.6.4"
    abort-controller "^3.0.0"
    agentkeepalive "^4.2.1"
    form-data-encoder "1.7.2"
    formdata-node "^4.3.2"
    node-fetch "^2.6.7"

retry@^0.13.1:
  version "0.13.1"
  resolved "https://registry.npmjs.org/retry/-/retry-0.13.1.tgz"
  integrity sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==

reusify@^1.0.4:
  version "1.1.0"
  resolved "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz"
  integrity sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
  integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
  dependencies:
    glob "^7.1.3"

robust-predicates@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/robust-predicates/-/robust-predicates-3.0.2.tgz"
  integrity sha512-IXgzBWvWQwE6PrDI05OvmXUIruQTcoMDzRsOd5CDvHCVLcLHMTSYvOK5Cm46kWqlV3yAbuSpBZdJ5oP5OUoStg==

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

rxjs@*:
  version "7.8.2"
  resolved "https://registry.npmjs.org/rxjs/-/rxjs-7.8.2.tgz"
  integrity sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==
  dependencies:
    tslib "^2.1.0"

safe-array-concat@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.1.3.tgz"
  integrity sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    has-symbols "^1.1.0"
    isarray "^2.0.5"

safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

safe-push-apply@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/safe-push-apply/-/safe-push-apply-1.0.0.tgz"
  integrity sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==
  dependencies:
    es-errors "^1.3.0"
    isarray "^2.0.5"

safe-regex-test@^1.0.3, safe-regex-test@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.1.0.tgz"
  integrity sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-regex "^1.2.1"

scheduler@^0.23.2:
  version "0.23.2"
  resolved "https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz"
  integrity sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==
  dependencies:
    loose-envify "^1.1.0"

scroll-into-view-if-needed@3.0.10:
  version "3.0.10"
  resolved "https://registry.npmjs.org/scroll-into-view-if-needed/-/scroll-into-view-if-needed-3.0.10.tgz"
  integrity sha512-t44QCeDKAPf1mtQH3fYpWz8IM/DyvHLjs8wUvvwMYxk5moOqCzrMSxK6HQVD0QVmVjXFavoFIPRVrMuJPKAvtg==
  dependencies:
    compute-scroll-into-view "^3.0.2"

sdp-transform@^2.15.0:
  version "2.15.0"
  resolved "https://registry.npmjs.org/sdp-transform/-/sdp-transform-2.15.0.tgz"
  integrity sha512-KrOH82c/W+GYQ0LHqtr3caRpM3ITglq3ljGUIb8LTki7ByacJZ9z+piSGiwZDsRyhQbYBOBJgr2k6X4BZXi3Kw==

sdp@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/sdp/-/sdp-3.2.0.tgz"
  integrity sha512-d7wDPgDV3DDiqulJjKiV2865wKsJ34YI+NDREbm+FySq6WuKOikwyNQcm+doLAZ1O6ltdO0SeKle2xMpN3Brgw==

semver@^6.3.1:
  version "6.3.1"
  resolved "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz"
  integrity sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==

semver@^7.5.4, semver@^7.6.3:
  version "7.7.1"
  resolved "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz"
  integrity sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==

set-cookie-parser@^2.6.0:
  version "2.7.1"
  resolved "https://registry.npmjs.org/set-cookie-parser/-/set-cookie-parser-2.7.1.tgz"
  integrity sha512-IOc8uWeOZgnb3ptbCURJWNjWUPcO3ZnTTdzsurqERrP6nPyv+paC55vJM0LpOlT2ne+Ix+9+CRG1MNLlyZ4GjQ==

set-function-length@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz"
  integrity sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

set-proto@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/set-proto/-/set-proto-1.0.0.tgz"
  integrity sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==
  dependencies:
    dunder-proto "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"

shadcn-ui@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/shadcn-ui/-/shadcn-ui-0.4.1.tgz"
  integrity sha512-6SMlEzoJzbkPFZ1zP01bOi90reiV8yYnBeTmqys37YJPfnNKDk8mtPq3jtoYGDLIEcmLdcI/vY12qxzxDCTKkw==
  dependencies:
    "@antfu/ni" "^0.21.4"
    "@babel/core" "^7.22.1"
    "@babel/parser" "^7.22.6"
    "@babel/plugin-transform-typescript" "^7.22.5"
    chalk "5.2.0"
    commander "^10.0.0"
    cosmiconfig "^8.1.3"
    diff "^5.1.0"
    execa "^7.0.0"
    fs-extra "^11.1.0"
    https-proxy-agent "^6.2.0"
    lodash.template "^4.5.0"
    node-fetch "^3.3.0"
    ora "^6.1.2"
    prompts "^2.4.2"
    recast "^0.23.2"
    ts-morph "^18.0.0"
    tsconfig-paths "^4.2.0"
    zod "^3.20.2"

sharp@^0.33.2:
  version "0.33.5"
  resolved "https://registry.npmjs.org/sharp/-/sharp-0.33.5.tgz"
  integrity sha512-haPVm1EkS9pgvHrQ/F3Xy+hgcuMV0Wm9vfIBSiwZ05k+xgb0PkBQpGsAA/oWdDobNaZTH5ppvHtzCFbnSEwHVw==
  dependencies:
    color "^4.2.3"
    detect-libc "^2.0.3"
    semver "^7.6.3"
  optionalDependencies:
    "@img/sharp-darwin-arm64" "0.33.5"
    "@img/sharp-darwin-x64" "0.33.5"
    "@img/sharp-libvips-darwin-arm64" "1.0.4"
    "@img/sharp-libvips-darwin-x64" "1.0.4"
    "@img/sharp-libvips-linux-arm" "1.0.5"
    "@img/sharp-libvips-linux-arm64" "1.0.4"
    "@img/sharp-libvips-linux-s390x" "1.0.4"
    "@img/sharp-libvips-linux-x64" "1.0.4"
    "@img/sharp-libvips-linuxmusl-arm64" "1.0.4"
    "@img/sharp-libvips-linuxmusl-x64" "1.0.4"
    "@img/sharp-linux-arm" "0.33.5"
    "@img/sharp-linux-arm64" "0.33.5"
    "@img/sharp-linux-s390x" "0.33.5"
    "@img/sharp-linux-x64" "0.33.5"
    "@img/sharp-linuxmusl-arm64" "0.33.5"
    "@img/sharp-linuxmusl-x64" "0.33.5"
    "@img/sharp-wasm32" "0.33.5"
    "@img/sharp-win32-ia32" "0.33.5"
    "@img/sharp-win32-x64" "0.33.5"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

side-channel-list@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz"
  integrity sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz"
  integrity sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz"
  integrity sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.0.4, side-channel@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz"
  integrity sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

signal-exit@^3.0.2, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

signal-exit@^4.0.1:
  version "4.1.0"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz"
  integrity sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz"
  integrity sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==
  dependencies:
    is-arrayish "^0.3.1"

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz"
  integrity sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz"
  integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==

snake-case@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmjs.org/snake-case/-/snake-case-3.0.4.tgz"
  integrity sha512-LAOh4z89bGQvl9pFfNF8V146i7o7/CqFPbqzYgP+yYzDIDeS9HaNFtXABamRW+AQzEVODcvE79ljJ+8a9YSdMg==
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

snakecase-keys@5.4.4:
  version "5.4.4"
  resolved "https://registry.npmjs.org/snakecase-keys/-/snakecase-keys-5.4.4.tgz"
  integrity sha512-YTywJG93yxwHLgrYLZjlC75moVEX04LZM4FHfihjHe1FCXm+QaLOFfSf535aXOAd0ArVQMWUAe8ZPm4VtWyXaA==
  dependencies:
    map-obj "^4.1.0"
    snake-case "^3.0.4"
    type-fest "^2.5.2"

sonner@^1.4.41:
  version "1.7.4"
  resolved "https://registry.npmjs.org/sonner/-/sonner-1.7.4.tgz"
  integrity sha512-DIS8z4PfJRbIyfVFDVnK9rO3eYDtse4Omcm6bt0oEr5/jtLgysmjuBl1frJ9E/EQZrFmKx2A8m/s5s9CRXIzhw==

source-map-js@^1.0.2, source-map-js@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz"
  integrity sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==

source-map@^0.5.7:
  version "0.5.7"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  integrity sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==

source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

stable-hash@^0.0.4:
  version "0.0.4"
  resolved "https://registry.npmjs.org/stable-hash/-/stable-hash-0.0.4.tgz"
  integrity sha512-LjdcbuBeLcdETCrPn9i8AYAZ1eCtu4ECAWtP7UleOiZ9LzVxRzzUZEoZ8zB24nhkQnDWyET0I+3sWokSDS3E7g==

std-env@^3.7.0:
  version "3.8.1"
  resolved "https://registry.npmjs.org/std-env/-/std-env-3.8.1.tgz"
  integrity sha512-vj5lIj3Mwf9D79hBkltk5qmkFI+biIKWS2IBxEyEU3AX1tUf7AoL8nSazCOiiqQsGKIq01SClsKEzweu34uwvA==

stdin-discarder@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/stdin-discarder/-/stdin-discarder-0.1.0.tgz"
  integrity sha512-xhV7w8S+bUwlPTb4bAOUQhv8/cSS5offJuX8GQGq32ONF0ZtDWKfkdomM3HMRA+LhX6um/FZ0COqlwsjD53LeQ==
  dependencies:
    bl "^5.0.0"

streamsearch@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz"
  integrity sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
  dependencies:
    safe-buffer "~5.2.0"

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.1.0:
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz"
  integrity sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string.prototype.includes@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/string.prototype.includes/-/string.prototype.includes-2.0.1.tgz"
  integrity sha512-o7+c9bW6zpAdJHTtujeePODAhkuicdAryFsfVKwA+wGw89wJ4GTY484WTucM9hLtDEOpOvI+aHnzqnC5lHp4Rg==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"

string.prototype.matchall@^4.0.12:
  version "4.0.12"
  resolved "https://registry.npmjs.org/string.prototype.matchall/-/string.prototype.matchall-4.0.12.tgz"
  integrity sha512-6CC9uyBL+/48dYizRf7H7VAYCMCNTBeM78x/VTUe9bFEaxBepPJDa1Ow99LqI/1yF7kuy7Q3cQsYMrcjGUcskA==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    regexp.prototype.flags "^1.5.3"
    set-function-name "^2.0.2"
    side-channel "^1.1.0"

string.prototype.repeat@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/string.prototype.repeat/-/string.prototype.repeat-1.0.0.tgz"
  integrity sha512-0u/TldDbKD8bFCQ/4f5+mNRrXwZ8hg2w7ZR8wa16e8z9XpePWl3eGEcUD0OXpEH/VJH/2G3gjUtR3ZOiBe2S/w==
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string.prototype.trim@^1.2.10:
  version "1.2.10"
  resolved "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.10.tgz"
  integrity sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-data-property "^1.1.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-object-atoms "^1.0.0"
    has-property-descriptors "^1.0.2"

string.prototype.trimend@^1.0.8, string.prototype.trimend@^1.0.9:
  version "1.0.9"
  resolved "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.9.tgz"
  integrity sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz"
  integrity sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz"
  integrity sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==
  dependencies:
    ansi-regex "^6.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
  integrity sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==

strip-final-newline@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-3.0.0.tgz"
  integrity sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  integrity sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==

styled-jsx@5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/styled-jsx/-/styled-jsx-5.1.1.tgz"
  integrity sha512-pW7uC1l4mBZ8ugbiZrcIsiIvVx1UmTfw7UkC3Um2tmfUq9Bhk8IiyEIPl6F8agHgjzku6j0xQEZbfA5uSgSaCw==
  dependencies:
    client-only "0.0.1"

stylis@4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/stylis/-/stylis-4.2.0.tgz"
  integrity sha512-Orov6g6BB1sDfYgzWfTHDOxamtX1bE/zo104Dh9e6fqJ3PooipYyfJ0pUmrZO2wAvO8YbEyeFrkV91XTsGMSrw==

sucrase@^3.35.0:
  version "3.35.0"
  resolved "https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz"
  integrity sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.2"
    commander "^4.0.0"
    glob "^10.3.10"
    lines-and-columns "^1.1.6"
    mz "^2.7.0"
    pirates "^4.0.1"
    ts-interface-checker "^0.1.9"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

swr@^2.2.0, swr@2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/swr/-/swr-2.2.0.tgz"
  integrity sha512-AjqHOv2lAhkuUdIiBu9xbuettzAzWXmCEcLONNKJRba87WAefz8Ca9d6ds/SzrPc235n1IxWYdhJ2zF3MNUaoQ==
  dependencies:
    use-sync-external-store "^1.2.0"

tabbable@^6.0.1:
  version "6.2.0"
  resolved "https://registry.npmjs.org/tabbable/-/tabbable-6.2.0.tgz"
  integrity sha512-Cat63mxsVJlzYvN51JmVXIgNoUokrIaT2zLclCXjRd8boZ0004U4KCs/sToJ75C6sdlByWxpYnb5Boif1VSFew==

tailwind-merge@^1.14.0:
  version "1.14.0"
  resolved "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-1.14.0.tgz"
  integrity sha512-3mFKyCo/MBcgyOTlrY8T7odzZFx+w+qKSMAmdFzRvqBfLlSigU6TZnlFHK0lkMwj9Bj8OYU+9yW9lmGuS0QEnQ==

tailwind-merge@^2.1.0, tailwind-merge@^2.5.2:
  version "2.6.0"
  resolved "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-2.6.0.tgz"
  integrity sha512-P+Vu1qXfzediirmHOC3xKGAYeZtPcV9g76X+xg2FD4tYgR71ewMA35Y3sCz3zhiN/dwefRpJX0yBcgwi1fXNQA==

tailwind-scrollbar-hide@^1.1.7:
  version "1.3.1"
  resolved "https://registry.npmjs.org/tailwind-scrollbar-hide/-/tailwind-scrollbar-hide-1.3.1.tgz"
  integrity sha512-eUAvPTltKnAGHbCBRpOk5S7+UZTkFZgDKmZLZ6jZXXs4V7mRXvwshBjeMwrv3vmiWqm3IGEDFVKzUSm1JuoXKw==

tailwind-variants@^0.1.20:
  version "0.1.20"
  resolved "https://registry.npmjs.org/tailwind-variants/-/tailwind-variants-0.1.20.tgz"
  integrity sha512-AMh7x313t/V+eTySKB0Dal08RHY7ggYK0MSn/ad8wKWOrDUIzyiWNayRUm2PIJ4VRkvRnfNuyRuKbLV3EN+ewQ==
  dependencies:
    tailwind-merge "^1.14.0"

tailwindcss-animate@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/tailwindcss-animate/-/tailwindcss-animate-1.0.7.tgz"
  integrity sha512-bl6mpH3T7I3UFxuvDEXLxy/VuFxBk5bbzplh7tXI68mwMokNYd1t9qPBHlnyTwfa4JGC4zP516I1hYYtQ/vspA==

tailwindcss@*, tailwindcss@^3.3.0, "tailwindcss@>=3.0.0 || >= 4.0.0 || >= 4.0.0-beta.8 || >= 4.0.0-alpha.20", "tailwindcss@>=3.0.0 || insiders", tailwindcss@>=3.4.0:
  version "3.4.17"
  resolved "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.4.17.tgz"
  integrity sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og==
  dependencies:
    "@alloc/quick-lru" "^5.2.0"
    arg "^5.0.2"
    chokidar "^3.6.0"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.3.2"
    glob-parent "^6.0.2"
    is-glob "^4.0.3"
    jiti "^1.21.6"
    lilconfig "^3.1.3"
    micromatch "^4.0.8"
    normalize-path "^3.0.0"
    object-hash "^3.0.0"
    picocolors "^1.1.1"
    postcss "^8.4.47"
    postcss-import "^15.1.0"
    postcss-js "^4.0.1"
    postcss-load-config "^4.0.2"
    postcss-nested "^6.2.0"
    postcss-selector-parser "^6.1.2"
    resolve "^1.22.8"
    sucrase "^3.35.0"

tapable@^2.2.0:
  version "2.2.1"
  resolved "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz"
  integrity sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  integrity sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz"
  integrity sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz"
  integrity sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==
  dependencies:
    any-promise "^1.0.0"

tiny-invariant@^1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.3.3.tgz"
  integrity sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==

tinycolor2@^1.4.1:
  version "1.6.0"
  resolved "https://registry.npmjs.org/tinycolor2/-/tinycolor2-1.6.0.tgz"
  integrity sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw==

tinyglobby@^0.2.12:
  version "0.2.12"
  resolved "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.12.tgz"
  integrity sha512-qkf4trmKSIiMTs/E63cxH+ojC2unam7rJ0WrauAzpT3ECNTxGRMlaXxVbfxMUC/w0LaYk6jQ4y/nGR9uBO3tww==
  dependencies:
    fdir "^6.4.3"
    picomatch "^4.0.2"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

toggle-selection@^1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/toggle-selection/-/toggle-selection-1.0.6.tgz"
  integrity sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz"
  integrity sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==

ts-api-utils@^1.0.1:
  version "1.4.3"
  resolved "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.4.3.tgz"
  integrity sha512-i3eMG77UTMD0hZhgRS562pv83RC6ukSAC2GMNWc+9dieh/+jDM5u5YG+NHX6VNDRHQcHwmsTHctP9LhbC3WxVw==

ts-debounce@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/ts-debounce/-/ts-debounce-4.0.0.tgz"
  integrity sha512-+1iDGY6NmOGidq7i7xZGA4cm8DAa6fqdYcvO5Z6yBevH++Bdo9Qt/mN0TzHUgcCcKv1gmh9+W5dHqz8pMWbCbg==

ts-interface-checker@^0.1.9:
  version "0.1.13"
  resolved "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz"
  integrity sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==

ts-morph@^18.0.0:
  version "18.0.0"
  resolved "https://registry.npmjs.org/ts-morph/-/ts-morph-18.0.0.tgz"
  integrity sha512-Kg5u0mk19PIIe4islUI/HWRvm9bC1lHejK4S0oh1zaZ77TMZAEmQC0sHQYiu2RgCQFZKXz1fMVi/7nOOeirznA==
  dependencies:
    "@ts-morph/common" "~0.19.0"
    code-block-writer "^12.0.0"

tsconfig-paths@^3.15.0:
  version "3.15.0"
  resolved "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.15.0.tgz"
  integrity sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tsconfig-paths@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-4.2.0.tgz"
  integrity sha512-NoZ4roiN7LnbKn9QqE1amc9DJfzvZXxF4xDavcOWt1BPkdx+m+0gJuPM+S0vCe7zTJMYUP0R8pO2XMr+Y8oLIg==
  dependencies:
    json5 "^2.2.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^2.0.0, tslib@^2.0.1, tslib@^2.0.3, tslib@^2.1.0, tslib@^2.4.0, tslib@2, tslib@2.4.1:
  version "2.4.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz"
  integrity sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==

tslib@^2.7.0:
  version "2.8.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

tslib@^2.8.0:
  version "2.8.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

tslib@2.8.1:
  version "2.8.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz"
  integrity sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz"
  integrity sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==

type-fest@^2.5.2:
  version "2.19.0"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-2.19.0.tgz"
  integrity sha512-RAH822pAdBgcNMAfWnCBU3CFZcfZ/i1eZjwFU/dsLKumyuuP3niueg2UAukXYF0E2AAoc82ZSSf9J0WQBinzHA==

typed-array-buffer@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/typed-array-buffer/-/typed-array-buffer-1.0.3.tgz"
  integrity sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-typed-array "^1.1.14"

typed-array-byte-length@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/typed-array-byte-length/-/typed-array-byte-length-1.0.3.tgz"
  integrity sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==
  dependencies:
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.14"

typed-array-byte-offset@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/typed-array-byte-offset/-/typed-array-byte-offset-1.0.4.tgz"
  integrity sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.15"
    reflect.getprototypeof "^1.0.9"

typed-array-length@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/typed-array-length/-/typed-array-length-1.0.7.tgz"
  integrity sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"
    reflect.getprototypeof "^1.0.6"

typed-emitter@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/typed-emitter/-/typed-emitter-2.1.0.tgz"
  integrity sha512-g/KzbYKbH5C2vPkaXGu8DJlHrGKHLsM25Zg9WuC9pMGfuvT+X25tZQWo5fK1BjBm8+UrVE9LDCvaY0CQk+fXDA==
  optionalDependencies:
    rxjs "*"

typescript@^5, typescript@>=3.3.1, typescript@>=4.2.0, typescript@>=4.9.5:
  version "5.8.2"
  resolved "https://registry.npmjs.org/typescript/-/typescript-5.8.2.tgz"
  integrity sha512-aJn6wq13/afZp/jT9QZmwEjDqqvSGp1VT5GVg+f/t6/oVyrgXM6BY1h9BRh/O5p3PlUPAe+WuiEZOmb/49RqoQ==

unbox-primitive@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.1.0.tgz"
  integrity sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==
  dependencies:
    call-bound "^1.0.3"
    has-bigints "^1.0.2"
    has-symbols "^1.1.0"
    which-boxed-primitive "^1.1.1"

undici-types@~5.26.4:
  version "5.26.5"
  resolved "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz"
  integrity sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==

undici-types@~6.19.2:
  version "6.19.8"
  resolved "https://registry.npmjs.org/undici-types/-/undici-types-6.19.8.tgz"
  integrity sha512-ve2KP6f/JnbPBFyobGHuerC9g1FYGn/F8n1LWTwNxCEzd6IfqTwUQcNXgEtmmQ6DlRrC1hrSrBnCZPokRrDHjw==

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz"
  integrity sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==

update-browserslist-db@^1.1.1:
  version "1.1.3"
  resolved "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz"
  integrity sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
  integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
  dependencies:
    punycode "^2.1.0"

use-callback-ref@^1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/use-callback-ref/-/use-callback-ref-1.3.3.tgz"
  integrity sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==
  dependencies:
    tslib "^2.0.0"

use-composed-ref@^1.3.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/use-composed-ref/-/use-composed-ref-1.4.0.tgz"
  integrity sha512-djviaxuOOh7wkj0paeO1Q/4wMZ8Zrnag5H6yBvzN7AKKe8beOaED9SF5/ByLqsku8NP4zQqsvM2u3ew/tJK8/w==

use-isomorphic-layout-effect@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npmjs.org/use-isomorphic-layout-effect/-/use-isomorphic-layout-effect-1.2.0.tgz"
  integrity sha512-q6ayo8DWoPZT0VdG4u3D3uxcgONP3Mevx2i2b0434cwWBoL+aelL1DzkXI6w3PhTZzUeR2kaVlZn70iCiseP6w==

use-latest@^1.2.1:
  version "1.3.0"
  resolved "https://registry.npmjs.org/use-latest/-/use-latest-1.3.0.tgz"
  integrity sha512-mhg3xdm9NaM8q+gLT8KryJPnRFOz1/5XPBhmDEVZK1webPzDjrPk7f/mbpeLqTgB9msytYWANxgALOCJKnLvcQ==
  dependencies:
    use-isomorphic-layout-effect "^1.1.1"

use-sidecar@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/use-sidecar/-/use-sidecar-1.1.3.tgz"
  integrity sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==
  dependencies:
    detect-node-es "^1.1.0"
    tslib "^2.0.0"

use-sync-external-store@^1.2.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.4.0.tgz"
  integrity sha512-9WXSPC5fMv61vaupRkCKCxsPxBocVnwakBEkMIHHpkTTg6icbJtg6jzgtLDm4bl3cSHAca52rYWih0k4K3PfHw==

util-deprecate@^1.0.1, util-deprecate@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

uuid@^10.0.0:
  version "10.0.0"
  resolved "https://registry.npmjs.org/uuid/-/uuid-10.0.0.tgz"
  integrity sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==

uuid@^9.0.0:
  version "9.0.1"
  resolved "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz"
  integrity sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz"
  integrity sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==
  dependencies:
    defaults "^1.0.3"

web-streams-polyfill@^3.0.3, web-streams-polyfill@^3.2.1:
  version "3.3.3"
  resolved "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz"
  integrity sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==

web-streams-polyfill@4.0.0-beta.3:
  version "4.0.0-beta.3"
  resolved "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-4.0.0-beta.3.tgz"
  integrity sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==

webrtc-adapter@^9.0.1:
  version "9.0.1"
  resolved "https://registry.npmjs.org/webrtc-adapter/-/webrtc-adapter-9.0.1.tgz"
  integrity sha512-1AQO+d4ElfVSXyzNVTOewgGT/tAomwwztX/6e3totvyyzXPvXIIuUUjAmyZGbKBKbZOXauuJooZm3g6IuFuiNQ==
  dependencies:
    sdp "^3.2.0"

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which-boxed-primitive@^1.1.0, which-boxed-primitive@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.1.1.tgz"
  integrity sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==
  dependencies:
    is-bigint "^1.1.0"
    is-boolean-object "^1.2.1"
    is-number-object "^1.1.1"
    is-string "^1.1.1"
    is-symbol "^1.1.1"

which-builtin-type@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/which-builtin-type/-/which-builtin-type-1.2.1.tgz"
  integrity sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==
  dependencies:
    call-bound "^1.0.2"
    function.prototype.name "^1.1.6"
    has-tostringtag "^1.0.2"
    is-async-function "^2.0.0"
    is-date-object "^1.1.0"
    is-finalizationregistry "^1.1.0"
    is-generator-function "^1.0.10"
    is-regex "^1.2.1"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.1.0"
    which-collection "^1.0.2"
    which-typed-array "^1.1.16"

which-collection@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/which-collection/-/which-collection-1.0.2.tgz"
  integrity sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==
  dependencies:
    is-map "^2.0.3"
    is-set "^2.0.3"
    is-weakmap "^2.0.2"
    is-weakset "^2.0.3"

which-typed-array@^1.1.16, which-typed-array@^1.1.18:
  version "1.1.18"
  resolved "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.18.tgz"
  integrity sha512-qEcY+KJYlWyLH9vNbsr6/5j59AXk5ni5aakf8ldzBvGde6Iz4sxZGkJyWSAueTG7QhOvNRYb1lDdFmL5Td0QKA==
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz"
  integrity sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz"
  integrity sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

ws@^8.14.2, ws@^8.18.0:
  version "8.18.1"
  resolved "https://registry.npmjs.org/ws/-/ws-8.18.1.tgz"
  integrity sha512-RKW2aJZMXeMxVpnZ6bck+RswznaxmzdULiBr6KY7XkTnW8uvt0iT9H5DkHUChXrc+uurzwa0rVI16n/Xzjdz1w==

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
  integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==

yaml@^1.10.0:
  version "1.10.2"
  resolved "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz"
  integrity sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==

yaml@^2.2.1:
  version "2.7.0"
  resolved "https://registry.npmjs.org/yaml/-/yaml-2.7.0.tgz"
  integrity sha512-+hSoy/QHluxmC9kCIJyL/uyFmLmc+e5CFR5Wa+bpIhIj85LVb9ZH2nVnqrHoSvKogwODv0ClqZkmiSSaIH5LTA==

yaml@^2.3.4:
  version "2.7.0"
  resolved "https://registry.npmjs.org/yaml/-/yaml-2.7.0.tgz"
  integrity sha512-+hSoy/QHluxmC9kCIJyL/uyFmLmc+e5CFR5Wa+bpIhIj85LVb9ZH2nVnqrHoSvKogwODv0ClqZkmiSSaIH5LTA==

yarn@^1.22.22:
  version "1.22.22"
  resolved "https://registry.npmjs.org/yarn/-/yarn-1.22.22.tgz"
  integrity sha512-prL3kGtyG7o9Z9Sv8IPfBNrWTDmXB4Qbes8A9rEzt6wkJV8mUvoirjU0Mp3GGAU06Y0XQyA3/2/RQFVuK7MTfg==

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"
  integrity sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==

zod-to-json-schema@^3.22.3, zod-to-json-schema@^3.22.5:
  version "3.24.3"
  resolved "https://registry.npmjs.org/zod-to-json-schema/-/zod-to-json-schema-3.24.3.tgz"
  integrity sha512-HIAfWdYIt1sssHfYZFCXp4rU1w2r8hVVXYIlmoa0r0gABLs5di3RCqPU5DDROogVz1pAdYBaz7HK5n9pSUNs3A==

zod@^3.20.2, zod@^3.22.3, zod@^3.22.4, zod@^3.23.8, zod@^3.24.1:
  version "3.24.2"
  resolved "https://registry.npmjs.org/zod/-/zod-3.24.2.tgz"
  integrity sha512-lY7CDW43ECgW9u1TcT3IoXHflywfVqDYze4waEz812jR/bZ8FHDsl7pFQoSZTz5N+2NqRXs8GBwnAwo3ZNxqhQ==
