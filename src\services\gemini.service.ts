import { GoogleGenerativeAI } from "@google/generative-ai";

// Interface to match OpenAI's response structure
interface ChatCompletionMessage {
  role: "system" | "user" | "assistant";
  content: string;
}

interface ChatCompletionChoice {
  message: {
    content: string;
  };
}

interface ChatCompletion {
  choices: ChatCompletionChoice[];
}

interface ChatCompletionCreateParams {
  model: string;
  messages: ChatCompletionMessage[];
  response_format?: { type: "json_object" };
}

// Gemini API service that mimics OpenAI's interface
export class GeminiService {
  private genAI: GoogleGenerativeAI;

  constructor(apiKey: string) {
    this.genAI = new GoogleGenerativeAI(apiKey);
  }

  async createChatCompletion(params: ChatCompletionCreateParams): Promise<ChatCompletion> {
    try {
      // Map OpenAI model to Gemini model
      const geminiModel = this.mapModelToGemini(params.model);
      const model = this.genAI.getGenerativeModel({ model: geminiModel });

      // Convert OpenAI messages format to Gemini format
      const prompt = this.convertMessagesToPrompt(params.messages);

      // Add JSON format instruction if needed
      const finalPrompt = params.response_format?.type === "json_object" 
        ? `${prompt}\n\nPlease respond with valid JSON only.`
        : prompt;

      // Generate content
      const result = await model.generateContent(finalPrompt);
      const response = await result.response;
      const text = response.text();

      // Return in OpenAI format
      return {
        choices: [
          {
            message: {
              content: text
            }
          }
        ]
      };
    } catch (error) {
      console.error("Gemini API error:", error);
      throw error;
    }
  }

  private mapModelToGemini(openaiModel: string): string {
    // Map OpenAI models to Gemini models
    const modelMap: { [key: string]: string } = {
      "gpt-4o-mini": "gemini-1.5-flash",
      "gpt-4o": "gemini-1.5-pro",
      "gpt-4": "gemini-1.5-pro",
      "gpt-3.5-turbo": "gemini-1.5-flash"
    };

    return modelMap[openaiModel] || "gemini-1.5-flash";
  }

  private convertMessagesToPrompt(messages: ChatCompletionMessage[]): string {
    let prompt = "";
    
    for (const message of messages) {
      if (message.role === "system") {
        prompt += `System: ${message.content}\n\n`;
      } else if (message.role === "user") {
        prompt += `User: ${message.content}\n\n`;
      } else if (message.role === "assistant") {
        prompt += `Assistant: ${message.content}\n\n`;
      }
    }

    return prompt.trim();
  }
}

// Factory function to create a Gemini service instance that mimics OpenAI
export function createGeminiClient(apiKey: string) {
  const geminiService = new GeminiService(apiKey);

  return {
    chat: {
      completions: {
        create: (params: ChatCompletionCreateParams) => geminiService.createChatCompletion(params)
      }
    }
  };
}
