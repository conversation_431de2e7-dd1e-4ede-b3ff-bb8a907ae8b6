"use client";

import { usePathname } from "next/navigation";
import Navbar from "@/components/navbar";
import SideMenu from "@/components/sideMenu";

export default function ClientLayoutContent({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  return (
    <>
      {!pathname.includes("/sign-in") &&
        !pathname.includes("/sign-up") && <Navbar />}
      <div className="flex flex-row h-screen">
        {!pathname.includes("/sign-in") &&
          !pathname.includes("/sign-up") && <SideMenu />}
        <div className="ml-[200px] pt-[64px] h-full overflow-y-auto flex-grow">
          {children}
        </div>
      </div>
    </>
  );
}
