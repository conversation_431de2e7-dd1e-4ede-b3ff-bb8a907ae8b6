{"[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "editor.formatOnSave": true, "editor.rulers": [100], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.insertFinalNewline": true, "typescript.tsdk": "./node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "eslint.format.enable": true, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "prettier.configPath": ".prettierrc.js", "eslint.workingDirectories": [{"directory": "./", "changeProcessCWD": true}]}